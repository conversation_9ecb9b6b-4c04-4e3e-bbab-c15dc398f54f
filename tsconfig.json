{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2015", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "strict": true, "strictPropertyInitialization": true, "lib": ["es2017", "ES2015", "dom"], "typeRoots": ["./typings", "./node_modules/miniprogram-api-typings"], "baseUrl": ".", "types": ["miniprogram-api-typings"]}, "include": ["./**/*.ts"], "exclude": ["node_modules"]}