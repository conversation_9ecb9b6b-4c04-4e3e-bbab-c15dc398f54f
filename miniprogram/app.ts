// app.ts
import { CURRENT_API_BASE_URL } from './config';
import { ApiResponse } from './types/api';
import 'miniprogram_npm/umtrack-wx/index';

interface IAppOption {
  globalData: {
    userInfo: any | null
    token: string | null
    loginPromise: Promise<void> | null
  }
  login: () => Promise<void>
  checkLogin: () => Promise<void>
  umengConfig: {
    appKey: string
    useOpenid: boolean
    autoGetOpenid: boolean
    debug: boolean
    uploadUserInfo: boolean
  }
}

interface UserInfo {
  id: number
  openid: string
  name: string | null
  avatar: string | null
}

interface LoginData {
  token: string
  user: UserInfo
}

App<IAppOption>({
  globalData: {
    userInfo: undefined,
    token: null,
    loginPromise: null as Promise<void> | null
  },
  onLaunch() {
    console.log('App Launch');
    // 检查登录状态
    this.checkLogin();
  },
  umengConfig: {
    appKey: '6809c230bc47b67d8348288a', //由友盟分配的APP_KEY
    // 使用Openid进行统计，此项为false时将使用友盟+uuid进行用户统计。
    // 使用Openid来统计微信小程序的用户，会使统计的指标更为准确，对系统准确性要求高的应用推荐使用Openid。
    useOpenid: true,
    // 使用openid进行统计时，是否授权友盟自动获取Openid，
    // 如若需要，请到友盟后台"设置管理-应用信息"(https://mp.umeng.com/setting/appset)中设置appId及secret
    autoGetOpenid: true,
    debug: true, //是否打开调试模式
    uploadUserInfo: true // 自动上传用户信息，设为false取消上传，默认为false
  },

  checkLogin() {
    // 如果已经有正在进行的登录请求，直接返回该请求
    if (this.globalData.loginPromise) {
      console.log('已有登录请求正在进行中');
      return this.globalData.loginPromise;
    }

    const token = wx.getStorageSync('token');
    const tokenExpireTime = wx.getStorageSync('tokenExpireTime');
    const now = Date.now();

    // 如果token存在且未过期，直接返回
    if (token && tokenExpireTime && now < tokenExpireTime) {
      console.log('Token 有效，无需重新登录');
      return Promise.resolve();
    }

    // 如果token不存在或已过期，重新登录
    console.log('Token 不存在或已过期，开始登录');
    this.globalData.loginPromise = this.login();
    return this.globalData.loginPromise;
  },

  login() {
    return new Promise<void>((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送 res.code 到后台换取 token
            wx.request({
              url: `${CURRENT_API_BASE_URL}/users/wechat/login`,
              method: 'POST',
              data: {
                code: res.code
              },
              success: (res) => {
                const { code, data, msg } = res.data as ApiResponse<LoginData>;
                if (code === 0 && data) {
                  // 保存 token 和用户信息
                  wx.setStorageSync('token', data.token);
                  wx.setStorageSync('userInfo', data.user);
                  // 设置过期时间为1天后
                  const expireTime = Date.now() + 24 * 60 * 60 * 1000;
                  wx.setStorageSync('tokenExpireTime', expireTime);
                  console.log('登录成功，token已保存');
                  // 清除登录请求Promise
                  this.globalData.loginPromise = null;
                  resolve();
                } else {
                  console.error('登录失败:', msg);
                  // 清除登录请求Promise
                  this.globalData.loginPromise = null;
                  reject(new Error(msg || '登录失败'));
                }
              },
              fail: (err) => {
                console.error('登录请求失败:', err);
                // 清除登录请求Promise
                this.globalData.loginPromise = null;
                reject(err);
              }
            });
          } else {
            console.error('获取登录code失败:', res.errMsg);
            // 清除登录请求Promise
            this.globalData.loginPromise = null;
            reject(new Error(res.errMsg));
          }
        },
        fail: (err) => {
          console.error('wx.login 调用失败:', err);
          // 清除登录请求Promise
          this.globalData.loginPromise = null;
          reject(err);
        }
      });
    });
  }
})