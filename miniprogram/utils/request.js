const { CURRENT_API_BASE_URL } = require('../config');
const app = getApp();

function request(options) {
  return new Promise(function (resolve, reject) {
    // 确保已登录
    app.checkLogin().then(function () {
      const token = wx.getStorageSync('token');
      const header = Object.assign({}, options.header, {
        Authorization: `Bearer ${token}`,
      });
      wx.request({
        ...options,
        header,
        success: function (res) {
          if (res.statusCode === 401) {
            // token 失效，重新登录
            app.login().then(function () {
              const newToken = wx.getStorageSync('token');
              wx.request({
                ...options,
                header: Object.assign({}, header, {
                  Authorization: `Bearer ${newToken}`,
                }),
                success: function (retryRes) {
                  if (retryRes.statusCode === 401) {
                    wx.showToast({ title: '认证失败，请重新登录', icon: 'none' });
                    reject(new Error('认证失败'));
                  } else {
                    resolve(retryRes.data);
                  }
                },
                fail: function (err) {
                  wx.showToast({ title: '网络错误', icon: 'none' });
                  reject(err);
                },
              });
            }).catch(function (e) {
              wx.showToast({ title: '登录失败', icon: 'none' });
              reject(e);
            });
          } else {
            resolve(res.data);
          }
        },
        fail: function (err) {
          wx.showToast({ title: '网络错误', icon: 'none' });
          reject(err);
        },
      });
    }).catch(function (e) {
      wx.showToast({ title: '登录失效，请重试', icon: 'none' });
      reject(e);
    });
  });
}

module.exports = {
  request,
}; 