// 最近访问问题缓存工具，最多缓存30条
const MAX_CACHE = 30;
const CACHE_KEY_PREFIX = 'question_cache_';
const CACHE_KEYS_LIST = 'question_cache_keys';

export function setQuestionCache(id: string, data: any) {
  try {
    wx.setStorageSync(CACHE_KEY_PREFIX + id, data);
    let keys: string[] = wx.getStorageSync(CACHE_KEYS_LIST) || [];
    keys = keys.filter(k => k !== id);
    keys.unshift(id);
    if (keys.length > MAX_CACHE) {
      const removed = keys.splice(MAX_CACHE);
      removed.forEach(rid => wx.removeStorageSync(CACHE_KEY_PREFIX + rid));
    }
    wx.setStorageSync(CACHE_KEYS_LIST, keys);
  } catch (e) {
    // 容量溢出等异常
    console.error('缓存写入失败', e);
  }
}

export function getQuestionCache(id: string) {
  try {
    return wx.getStorageSync(CACHE_KEY_PREFIX + id);
  } catch (e) {
    return null;
  }
} 