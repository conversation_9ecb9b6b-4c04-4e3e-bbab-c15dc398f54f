.question-item {
  padding: 28rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  min-height: 180rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
}

.question-item:active {
  transform: scale(0.98);
}

.question-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.5;
  font-weight: 500;
}

.question-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.question-type {
  margin-right: 20rpx;
  padding: 6rpx 16rpx;
  background: #f5f7fa;
  border-radius: 6rpx;
  color: #666;
}

.question-company {
  color: #666;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  padding: 6rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  line-height: 1.4;
  font-weight: 500;
}

.tag.pm-type {
  background: #e6f7ff;
  color: #1890ff;
}

.tag.company {
  background: #f6ffed;
  color: #52c41a;
}

.tag.category {
  background: #fff7e6;
  color: #fa8c16;
} 