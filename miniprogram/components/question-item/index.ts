import { request } from '../../utils/request';
import { CURRENT_API_BASE_URL } from '../../config';
import { getQuestionCache, setQuestionCache } from '../../utils/questionCache';

interface Question {
  id: string;
  title: string;
  companyName?: string;
  pmTypeName?: string;
  category?: string[];
}

let isLoading = false;

Component({
  properties: {
    question: {
      type: Object,
      value: {},
      observer: function(this: any, newVal: any) {
        if (!newVal) return;

        let displayQuestion: Question;

        // 处理收藏夹的数据结构 (Favorites might pass { question: { id: ... } })
        if (newVal.question && newVal.question.id) {
          displayQuestion = {
            id: newVal.question.id,
            title: newVal.question.title,
            companyName: newVal.question.companyName || '',
            pmTypeName: newVal.question.pmTypeName || ''
          };
        } else { // 处理题库、历史记录等直接传入 { id: ... } 的数据结构
          displayQuestion = {
            id: newVal.id, // Expect id directly on newVal
            title: newVal.title,
            companyName: newVal.companyName || '',
            pmTypeName: newVal.pmTypeName || '',
            category: newVal.category
          };
        }

        this.setData({
          displayQuestion
        });
      }
    },
    index: {
      type: Number,
      value: 0
    },
    questionIds: {
      type: Array,
      value: []
    }
  },

  data: {
    displayQuestion: {} as Question
  },

  methods: {
    parseAnswerToRichText(answer: string): any[] {
      const lines = answer.split('\n');
      const nodes: any[] = [];
      lines.forEach(line => {
        if (line.trim() === '') {
          nodes.push({
            name: 'div',
            attrs: { class: 'empty-line' },
            children: [{ type: 'text', text: '\n' }]
          });
        } else if (line.match(/^\d+\./)) {
          nodes.push({
            name: 'div',
            attrs: { class: 'list-item' },
            children: [{ type: 'text', text: line }]
          });
        } else if (line.match(/^-\s/)) {
          nodes.push({
            name: 'div',
            attrs: { class: 'list-item' },
            children: [{ type: 'text', text: line }]
          });
        } else {
          nodes.push({
            name: 'div',
            attrs: { class: 'text-line' },
            children: [{ type: 'text', text: line }]
          });
        }
      });
      return nodes;
    },
    async onTap(this: any) {
      const id = this.data.displayQuestion.id;
      if (!id || isLoading) {
        return;
      }

      console.log('question-item onTap - component data:', {
        index: this.data.index,
        id: id,
        questionIds: this.data.questionIds ? this.data.questionIds.length : 'undefined'
      });

      // 触发父组件的tap事件，传递正确的参数
      this.triggerEvent('tap', {
        index: this.data.index,
        id: id,
        questionIds: this.data.questionIds
      });

      const cacheData = getQuestionCache(id);
      if (cacheData) {
        // 有缓存直接返回，什么都不用做
        return;
      }

      // 没有缓存，异步预请求（不阻塞跳转）
      isLoading = true;
      try {
        const res = await request({
          url: `${CURRENT_API_BASE_URL}/questions/${id}`,
          method: 'GET',
        });
        if (res && res.code === 0 && res.data) {
          const answerNodes = this.parseAnswerToRichText(res.data.answer || '');
          const cacheData = {
            ...res.data,
            answerNodes
          };
          setQuestionCache(id, cacheData); // 只写最近访问缓存
        }
      } catch (e) {
        // 忽略异常
      } finally {
        isLoading = false;
      }
    }
  }
});