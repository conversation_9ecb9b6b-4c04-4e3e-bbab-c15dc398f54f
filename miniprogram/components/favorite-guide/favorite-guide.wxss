.favorite-guide-wrapper {
  position: fixed;
  top: 20rpx;
  right: 32rpx;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.favorite-guide {
  background: rgba(0,0,0,0.8);
  color: #fff;
  padding: 24rpx 36rpx;
  border-radius: 22rpx;
  font-size: 28rpx;
  width: 440rpx;
  text-align: center;
  box-sizing: border-box;
}

.triangle-up {
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 16rpx solid rgba(0,0,0,0.8);
  margin-bottom: -2rpx;
}

.triangle-up-wrapper {
  width: 130rpx;
  display: flex;
} 