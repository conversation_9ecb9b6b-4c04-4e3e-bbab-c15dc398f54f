Component({
  data: {
    show: false,
    guideTop: 100, // 默认值
    guideLeft: 0   // 默认值
  },
  lifetimes: {
    attached: function () {
      const that = this as any;
      // 获取胶囊按钮位置
      if (wx.getMenuButtonBoundingClientRect) {
        const menuButton = wx.getMenuButtonBoundingClientRect();
        that.setData({
          guideTop: menuButton.top + menuButton.height - 60,
          guideLeft: menuButton.left + menuButton.width / 2 - 40
        });
      }
      if (wx.checkIsAddedToMyMiniProgram) {
        wx.checkIsAddedToMyMiniProgram({
          success: (res) => {
            // 正确字段为 res.added
            if (res.added === false) {
              that.setData({ show: true });
              setTimeout(() => {
                that.setData({ show: false });
              }, 5000);
            }
          },
          fail: () => {
            // 兼容处理：API不可用时不显示
          }
        });
      }
    }
  }
}); 