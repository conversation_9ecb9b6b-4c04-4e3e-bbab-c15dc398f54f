/* contact.wxss */
.container {
  background: linear-gradient(180deg, #f3f6fb 0%, #e8f2ff 100%);
  min-height: 100vh;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

/* 联系信息卡片 */
.contact-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx 36rpx;
  margin-bottom: 40rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(64, 158, 255, 0.12),
    0 2rpx 8rpx rgba(64, 158, 255, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.contact-icon {
  text-align: center;
  margin-bottom: 32rpx;
}

.icon {
  width: 120rpx;
  height: 120rpx;
}

.contact-info {
  text-align: center;
}

.contact-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.contact-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.wechat-info {
  background: rgba(64, 158, 255, 0.05);
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  border: 2rpx solid rgba(64, 158, 255, 0.1);
}

.wechat-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.wechat-id {
  font-size: 40rpx;
  font-weight: 600;
  color: #409EFF;
  font-family: 'Monaco', 'Menlo', monospace;
  letter-spacing: 2rpx;
}

/* 复制按钮 */
.copy-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  border-radius: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.copy-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.4);
}

.copy-icon {
  width: 32rpx;
  height: 32rpx;
}

.copy-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

/* 服务时间 */
.service-time {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.time-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.time-desc {
  font-size: 28rpx;
  color: #666;
}

/* 温馨提示 */
.tips {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.tips-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20rpx;
}

.tips-content {
  line-height: 1.6;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}
