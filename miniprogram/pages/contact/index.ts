interface IPageData {
  wechatId: string;
}

interface IPageInstance {
  data: IPageData;
  onLoad: () => void;
  copyWechatId: () => void;
}

Page<IPageData, IPageInstance>({
  data: {
    wechatId: 'sail19971025'
  },

  onLoad() {
    // 页面加载时的初始化
    wx.setNavigationBarTitle({
      title: '联系我们'
    });
  },

  copyWechatId() {
    const { wechatId } = this.data;
    
    wx.setClipboardData({
      data: wechatId,
      success: () => {
        // 添加触觉反馈
        wx.vibrateShort({
          type: 'light'
        });
        
        wx.showToast({
          title: '复制成功',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
});
