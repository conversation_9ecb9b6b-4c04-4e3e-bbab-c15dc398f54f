import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse, PaginatedResponse } from '../../types/api';
import { request } from '../../utils/request';

interface Question {
  id: string;
  title: string;
  tags: string[];
  description: string;
  answer: string;
  keyPoints: string[];
  views: number;
  likes: number;
  isLiked: boolean;
  isCollected: boolean;
  companyName: string;
  pmTypeName: string;
}

Page({
  data: {
    searchQuery: '',
    questions: [] as Question[],
    loading: false,
    page: 1,
    hasMore: true
  },

  onLoad(options: { title?: string }) {
    if (options.title) {
      this.setData({
        searchQuery: options.title
      }, () => {
        this.searchQuestions();
      });
    }
  },

  onSearch(e: WechatMiniprogram.Input) {
    const searchQuery = e.detail.value.trim();
    this.setData({
      searchQuery,
      questions: [],
      page: 1,
      hasMore: true
    }, () => {
      this.searchQuestions();
    });
  },

  async searchQuestions(): Promise<void> {
    if (this.data.loading) {
      return;
    }
    this.setData({ loading: true });
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions/search`,
        method: 'GET',
        data: {
          title: this.data.searchQuery,
          page: this.data.page,
          limit: 20
        },
      });
      const { code, data, msg } = res as PaginatedResponse<Question>;
      if (code !== 0) {
        wx.showToast({
          title: msg || '搜索失败',
          icon: 'none'
        });
        this.setData({ loading: false });
        return;
      }
      const newQuestions = data.list.map((item: any) => ({
        id: item.id,
        title: item.title,
        tags: item.tags || [],
        description: item.description || '',
        answer: item.answer || '',
        keyPoints: item.keyPoints || [],
        views: item.views || 0,
        likes: item.likes || 0,
        isLiked: item.isLiked || false,
        isCollected: item.isCollected || false,
        companyName: item.companyName || '',
        pmTypeName: item.pmTypeName || ''
      }));
      this.setData({
        questions: [...this.data.questions, ...newQuestions],
        page: this.data.page + 1,
        hasMore: newQuestions.length === 20,
        loading: false
      });
    } catch (err) {
      console.error('搜索失败', err);
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.searchQuestions();
    }
  },

  goToDetail(e: WechatMiniprogram.TouchEvent) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/question-detail/index?id=${id}`
    });
  }
}); 