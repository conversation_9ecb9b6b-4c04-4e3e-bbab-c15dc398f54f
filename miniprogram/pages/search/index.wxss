.container {
  padding: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  margin: 20rpx 30rpx;
  width: 90%;
}

.search-box .icon-search {
  font-size: 32rpx;
  color: #999;
  margin-right: 16rpx;
}

.search-box input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  color: #999;
}

.question-list {
  margin-top: 20rpx;
}

.question-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.tag {
  font-size: 24rpx;
  color: #666;
  background: #f5f5f5;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.question-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
}

.stat-item .iconfont {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.loading, .no-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 28rpx;
} 