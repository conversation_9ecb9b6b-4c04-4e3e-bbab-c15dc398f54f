import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse, PaginatedResponse } from '../../types/api';
import { trackEvent } from 'umtrack-wx';
import { getQuestionCache, setQuestionCache } from '../../utils/questionCache';
const { request } = require('../../utils/request');

interface Category {
  id: string;
  name: string;
  icon?: string;
  count: number;
}

interface Question {
  id: string;
  title: string;
  tags: string[];
  description: string;
  answer: string;
  keyPoints: string[];
  views: number;
  likes: number;
  isLiked: boolean;
  isCollected: boolean;
  pmTypeName: string;
  companyName: string;
  category: string[];
}

interface PMType {
  id: string;
  name: string;
  questionCount: number;
}

interface Company {
  id: string;
  name: string;
  questionCount: number;
}

// 全局缓存变量
let allQuestionsCache: {
  questions: Question[];
  searchQuery: string;
  pmTypeId: string;
  companyId: string;
} | null = null;

Page({
  data: {
    currentTab: 'all',
    scrollLeft: 0,
    questions: [] as Question[],
    categories: [] as Category[],
    pmTypes: [] as Array<{id: string; name: string; questionCount: number; displayName: string}>,
    companies: [] as Array<{id: string; name: string; questionCount: number}>,
    searchQuery: '',
    loading: false,
    page: 1,
    hasMore: true,
    pmTypeId: '',
    currentPmTypeDisplayName: '',
    companyId: '',
    currentCompanyName: '',
    pmTypesLoaded: false,
    companiesLoaded: false,
    pmTypesLoading: false,
    companiesLoading: false,
    pmType: '',
    company: '',
    allQuestionsCache: null as {
      questions: Question[];
      searchQuery: string;
      pmTypeId: string;
      companyId: string;
    } | null,
    questionIds: [] as string[],
    statusBarHeight: 0,
    navBarHeight: 0
  },

  onLoad() {
    // 获取系统信息，计算导航栏高度
    this.getSystemInfo();

    // 优先读取本地缓存
    const listCache = wx.getStorageSync('question_list_cache');
    if (listCache && Array.isArray(listCache.questions)) {
      this.setData({
        questions: listCache.questions,
        questionIds: listCache.questions.map((q: any) => q.id),
        // 其它相关字段可按需补充
      });
    }

    // 加载分类数据缓存
    this.loadCategoriesWithCache();

    console.log('页面加载');
    this.initData();
  },

  getSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    // 导航栏高度 = 状态栏高度 + 44px（标准导航栏高度）
    const navBarHeight = statusBarHeight + 44;

    this.setData({
      statusBarHeight,
      navBarHeight
    });
  },

  loadCategoriesWithCache() {
    // 检查PM类型缓存
    const pmTypesCache = wx.getStorageSync('pm_types_cache');
    if (pmTypesCache && Array.isArray(pmTypesCache) && pmTypesCache.length > 0) {
      console.log('从缓存加载PM类型数据');
      this.setData({
        pmTypes: pmTypesCache.map((item: any) => ({
          ...item,
          displayName: item.name.replace(/经理$/, '')
        })),
        pmTypesLoaded: true
      });
    } else {
      // 没有缓存，显示loading并请求数据
      this.setData({ pmTypesLoading: true });
    }

    // 检查公司缓存
    const companiesCache = wx.getStorageSync('companies_cache');
    if (companiesCache && Array.isArray(companiesCache) && companiesCache.length > 0) {
      console.log('从缓存加载公司数据');
      this.setData({
        companies: companiesCache,
        companiesLoaded: true
      });
    } else {
      // 没有缓存，显示loading并请求数据
      this.setData({ companiesLoading: true });
    }

    // 后台更新缓存
    if (!this.data.pmTypesLoaded) {
      this.loadPMTypes();
    }
    if (!this.data.companiesLoaded) {
      this.loadCompanies();
    }

    // 即使有缓存，也在后台更新数据
    this.updateCategoriesInBackground();
  },

  async updateCategoriesInBackground() {
    // 后台更新PM类型数据
    try {
      const token = wx.getStorageSync('token');
      if (token) {
        const pmTypesRes = await request({
          url: `${CURRENT_API_BASE_URL}/pm-type/with-count`,
          method: 'GET',
        });
        if (pmTypesRes && pmTypesRes.code === 0) {
          const newPmTypes = pmTypesRes.data.map((item: any) => ({
            ...item,
            displayName: item.name.replace(/经理$/, '')
          }));

          // 更新缓存
          wx.setStorageSync('pm_types_cache', pmTypesRes.data);

          // 更新页面数据
          this.setData({
            pmTypes: newPmTypes,
            pmTypesLoaded: true
          });
        }
      }
    } catch (err) {
      console.error('后台更新PM类型失败:', err);
    }

    // 后台更新公司数据
    try {
      const token = wx.getStorageSync('token');
      if (token) {
        const companiesRes = await request({
          url: `${CURRENT_API_BASE_URL}/company/with-count`,
          method: 'GET',
        });
        if (companiesRes && companiesRes.code === 0) {
          // 更新缓存
          wx.setStorageSync('companies_cache', companiesRes.data);

          // 更新页面数据
          this.setData({
            companies: companiesRes.data,
            companiesLoaded: true
          });
        }
      }
    } catch (err) {
      console.error('后台更新公司数据失败:', err);
    }
  },

  onReady() {
    // 页面渲染完成后，后台刷新列表
    this.fetchQuestionListAndUpdateCache();
  },

  async fetchQuestionListAndUpdateCache() {
    // 只在全部tab第一页且无筛选/搜索时刷新缓存
    if (
      this.data.currentTab === 'all' &&
      this.data.page === 1 &&
      !this.data.searchQuery &&
      !this.data.pmTypeId &&
      !this.data.companyId
    ) {
      try {
        const res = await request({
          url: `${CURRENT_API_BASE_URL}/questions`,
          method: 'GET',
          data: { page: 1, limit: 25 }
        });
        if (res && res.code === 0 && Array.isArray(res.data.list)) {
          this.setData({
            questions: res.data.list,
            questionIds: res.data.list.map(q => q.id),
          });
          wx.setStorageSync('question_list_cache', {
            questions: res.data.list,
            searchQuery: '',
            pmTypeId: '',
            companyId: ''
          });
        }
      } catch (e) {
        // 忽略异常
      }
    }
  },

  async initData() {
    console.log('初始化数据');
    const token = wx.getStorageSync('token');
    console.log('当前token:', token);

    if (token) {
      console.log('已有token，直接加载数据');
      console.log('走到这儿bbbbbb');
      await this.loadData();
    } else {
      console.log('未找到token，等待app.ts中的登录完成');
      const app = getApp<IAppOption>();
      try {
        await app.checkLogin();
        console.log('登录状态检查完成，开始加载数据');
        console.log('走到这儿ccccccc');
        await this.loadData();
      } catch (error) {
        console.error('登录失败:', error);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    }
  },

  onPullDownRefresh() {
    wx.showNavigationBarLoading();

    // 清空所有缓存
    console.log('下拉刷新：清空所有缓存');
    wx.removeStorageSync('question_list_cache');
    wx.removeStorageSync('pm_types_cache');
    wx.removeStorageSync('companies_cache');
    allQuestionsCache = null;

    const resetData: any = {
      questions: [],
      page: 1,
      hasMore: true,
      pmTypesLoaded: false,
      companiesLoaded: false,
      pmTypesLoading: false,
      companiesLoading: false,
      pmTypes: [],
      companies: [],
      allQuestionsCache: null
    };

    this.setData(resetData, async () => {
      // 重新加载分类数据
      this.loadCategoriesWithCache();

      console.log('下拉刷新：重新加载数据');
      await this.loadData();
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载更多');
    if (this.data.hasMore && !this.data.loading) {
      this.fetchQuestions();
    }
  },

  fetchQuestions(): Promise<void> {
    // 新增逻辑：如果是在分类Tab (direction 或 company) 且没有选择具体分类ID (pmType/company)，则不加载题目。
    if ((this.data.currentTab === 'direction' && !this.data.pmType) ||
        (this.data.currentTab === 'company' && !this.data.company)) {
      this.setData({
        questions: [],
        page: 1,
        hasMore: false, // 没有加载，所以没有更多
        loading: false
      });
      console.log(`[fetchQuestions] Tab: ${this.data.currentTab}, 未选择具体分类 (pmType: ${this.data.pmType}, company: ${this.data.company})，不加载题目列表。`);
      return Promise.resolve(); // 直接返回，不执行后续请求
    }

    console.log('[fetchQuestions] 开始请求题目列表，页码:', this.data.page);

    // 如果是第一页且没有题目，设置loading状态
    if (this.data.page === 1 && this.data.questions.length === 0) {
      this.setData({ loading: true });
    }

    return new Promise<void>((resolve, reject) => {
      // 构建请求参数
      const requestData: any = {
        page: this.data.page,
        limit: 25
      };

      // 只有当tab不是'all'时才添加tab参数
      if (this.data.currentTab !== 'all') {
        requestData.tab = this.data.currentTab;
      }

      // 只有当searchQuery不为空时才添加search参数
      if (this.data.searchQuery) {
        requestData.search = this.data.searchQuery;
      }

      // 如果当前有选中的PM类型，添加pmTypeId参数
      if (this.data.pmTypeId) {
        requestData.pmTypeId = this.data.pmTypeId;
      }

      // 如果当前有选中的公司，添加companyId参数
      if (this.data.companyId) {
        requestData.companyId = this.data.companyId;
      }

      console.log('[fetchQuestions] 请求参数:', requestData);

      const makeRequest = async () => {
        try {
          const res = await request({
            url: `${CURRENT_API_BASE_URL}/questions`,
            method: 'GET',
            data: requestData,
          });

          console.log('[fetchQuestions] 请求成功，返回数据:', res.data);
          const { code, data, msg } = res as PaginatedResponse<Question>;

          if (code !== 0) {
            wx.showToast({
              title: msg || '获取题目失败',
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(new Error(msg));
            return;
          }

          const newQuestions = data.list.map((item: Question) => ({
            id: item.id,
            title: item.title,
            tags: item.tags || [],
            description: item.description || '',
            answer: item.answer || '',
            keyPoints: item.keyPoints || [],
            views: item.views || 0,
            likes: item.likes || 0,
            isLiked: item.isLiked || false,
            isCollected: item.isCollected || false,
            pmTypeName: item.pmTypeName || '',
            companyName: item.companyName || '',
            category: item.category || []
          }));


          this.setData({
            questions: [...this.data.questions, ...newQuestions],
            questionIds: [...(this.data.questionIds || []), ...newQuestions.map(q => q.id)],
            page: this.data.page + 1,
            hasMore: newQuestions.length === 25,
            loading: false
          });
          // 只在全部tab第一页且无搜索/筛选时写入本地缓存
          if (
            this.data.currentTab === 'all' &&
            requestData.page === 1 &&
            !this.data.searchQuery &&
            !this.data.pmTypeId &&
            !this.data.companyId
          ) {
            allQuestionsCache = {
              questions: [...newQuestions],
              searchQuery: '',
              pmTypeId: '',
              companyId: ''
            };

            wx.setStorageSync('question_list_cache', {
              questions: [...newQuestions],
              searchQuery: '',
              pmTypeId: '',
              companyId: ''
            });

            this.setData({ allQuestionsCache }); // 仅用于调试
          }
          resolve();
        } catch (err: any) {
          console.error('获取题目列表失败', err);

          // 如果是401错误，尝试重新登录并重试请求
          if (err.statusCode === 401) {
            console.log('Token已过期，尝试重新登录');
            // 优先展示后端返回的msg
            let msg = '登录已过期，请重试';
            if (err.data && err.data.msg) {
              msg = err.data.msg;
            }
            wx.showToast({
              title: msg,
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(err);
          } else {
            wx.showToast({
              title: err.message || '获取题目失败',
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(err);
          }
        }
      };

      makeRequest().catch(reject);
    });
  },

  onScroll(e: any) {
    this.setData({
      scrollLeft: e.detail.scrollLeft
    });
  },

  switchTab(e: any) {
    const newTab = e.currentTarget.dataset.tab;
    if (this.data.currentTab === newTab) return;

    trackEvent('top_tab_click', { tabName: newTab });

    // 如果切到全部tab，优先读取缓存
    if (newTab === 'all') {
      if (allQuestionsCache && allQuestionsCache.questions && allQuestionsCache.questions.length > 0) {
        this.setData({
          currentTab: newTab,
          questions: allQuestionsCache.questions,
          questionIds: allQuestionsCache.questions.map(q => q.id),
          page: 2, // 缓存的是第一页，所以下一页应为2
          hasMore: true,
          searchQuery: '',
          pmTypeId: '',
          pmType: '',
          currentPmTypeDisplayName: '',
          companyId: '',
          company: '',
          currentCompanyName: ''
        }, () => {
          // 滚动位置逻辑保留
          const query = wx.createSelectorQuery();
          query.select(`#tab-${newTab}`).boundingClientRect();
          query.select('.tab-scroll').boundingClientRect();
          query.exec((res) => {
            if (res && res[0] && res[1]) {
              const tabRect = res[0];
              const scrollRect = res[1];
              if (tabRect && scrollRect) {
                const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
                this.setData({ scrollLeft });
              }
            }
          });
        });
        return;
      }
      // 没有缓存才请求接口
      this.setData({
        currentTab: newTab,
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        pmTypeId: '',
        pmType: '',
        currentPmTypeDisplayName: '',
        companyId: '',
        company: '',
        currentCompanyName: ''
      }, () => {
        this.fetchQuestions();
        const query = wx.createSelectorQuery();
        query.select(`#tab-${newTab}`).boundingClientRect();
        query.select('.tab-scroll').boundingClientRect();
        query.exec((res) => {
          if (res && res[0] && res[1]) {
            const tabRect = res[0];
            const scrollRect = res[1];
            if (tabRect && scrollRect) {
              const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
              this.setData({ scrollLeft });
            }
          }
        });
      });
      return;
    }

    // 切到方向/公司tab，只切tab，不请求题目列表
    if (newTab === 'direction' || newTab === 'company') {
      this.setData({ currentTab: newTab });
      // 滚动位置逻辑保留
      const query = wx.createSelectorQuery();
      query.select(`#tab-${newTab}`).boundingClientRect();
      query.select('.tab-scroll').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const tabRect = res[0];
          const scrollRect = res[1];
          if (tabRect && scrollRect) {
            const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
            this.setData({ scrollLeft });
          }
        }
      });
      return;
    }


    // 其他tab逻辑保持不变
    this.setData({
      currentTab: newTab,
      questions: [],
      page: 1,
      hasMore: true,
      searchQuery: '',
      pmTypeId: '',
      pmType: '',
      currentPmTypeDisplayName: '',
      companyId: '',
      company: '',
      currentCompanyName: ''
    }, () => {
      this.fetchQuestions();
      const query = wx.createSelectorQuery();
      query.select(`#tab-${newTab}`).boundingClientRect();
      query.select('.tab-scroll').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const tabRect = res[0];
          const scrollRect = res[1];
          if (tabRect && scrollRect) {
            const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
            this.setData({ scrollLeft });
          }
        }
      });
    });
  },

  onSearch(e: WechatMiniprogram.Input) {
    const value = e.detail.value;
    console.log('搜索关键词:', value);
    this.setData({
      searchQuery: value,
      questions: [],
      page: 1,
      hasMore: true,
      loading: true // 显示loading状态
    }, () => {
      this.fetchQuestions();
    });
  },


  async toggleCollect(e: WechatMiniprogram.TouchEvent) {
    const id = e.currentTarget.dataset.id;
    console.log('切换收藏状态, ID:', id);
    const questions = this.data.questions.map(q => {
      if (q.id === id) {
        return { ...q, isCollected: !q.isCollected };
      }
      return q;
    });
    this.setData({ questions });
    // 调用收藏/取消收藏接口
    const question = this.data.questions.find(q => q.id === id);
    if (question) {
      console.log('发送收藏请求, ID:', id, '当前状态:', question.isCollected);
      try {
        const res = await request({
          url: `${CURRENT_API_BASE_URL}/questions/${id}/collect`,
          method: question.isCollected ? 'POST' : 'DELETE',
        });
        const { code, msg } = res as ApiResponse;
        if (code !== 0) {
          wx.showToast({
            title: msg || '操作失败',
            icon: 'none'
          });
          return;
        }
        console.log('收藏状态更新成功', res);
      } catch (err) {
        console.error('收藏状态更新失败', err);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    }
  },

  goToCategory(e: WechatMiniprogram.TouchEvent) {
    const { id, name } = e.currentTarget.dataset;
    console.log('[goToCategory] 点击分类', { id, name, currentTab: this.data.currentTab });

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    if (this.data.currentTab === 'direction') {
      const selectedType = this.data.pmTypes.find(type => type.id === id);
      this.setData({
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        pmTypeId: id,
        pmType: id,
        currentPmTypeDisplayName: selectedType?.displayName || name || '',
        companyId: '',
        company: '',
        currentCompanyName: '',
        loading: true // 显示loading状态
      }, () => {
        console.log('[goToCategory] direction setData后 pmTypeId:', this.data.pmTypeId);
        this.fetchQuestions();
      });
    } else if (this.data.currentTab === 'company') {
      this.setData({
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        companyId: id,
        company: id,
        currentCompanyName: name,
        pmTypeId: '',
        pmType: '',
        currentPmTypeDisplayName: '',
        loading: true // 显示loading状态
      }, () => {
        console.log('[goToCategory] company setData后 companyId:', this.data.companyId);
        this.fetchQuestions();
      });
    }
  },

  goBackToCategories() {
    this.setData({
      pmTypeId: '',
      pmType: '',
      currentPmTypeDisplayName: '',
      companyId: '',
      company: '',
      currentCompanyName: '',
      questions: [],
      page: 1,
      hasMore: true,
      loading: true // 显示loading状态
    }, () => {
      this.fetchQuestions();
    });
  },

  async loadPMTypes() {
    try {
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('未找到token，等待app.ts中的登录完成');
        this.setData({ pmTypesLoading: false });
        return;
      }

      // 设置loading状态
      this.setData({ pmTypesLoading: true });

      const res = await request({
        url: `${CURRENT_API_BASE_URL}/pm-type/with-count`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<PMType[]>;
      if (code !== 0) {
        throw new Error(msg || '获取PM类型失败');
      }

      const processedData = data.map(item => ({
        ...item,
        displayName: item.name.replace(/经理$/, '')
      }));

      // 更新缓存
      wx.setStorageSync('pm_types_cache', data);

      this.setData({
        pmTypes: processedData,
        pmTypesLoaded: true,
        pmTypesLoading: false
      });

      console.log('PM类型数据加载完成并缓存');
    } catch (error) {
      console.error('获取PM类型失败:', error);
      this.setData({ pmTypesLoading: false });
      wx.showToast({
        title: '加载方向分类失败',
        icon: 'none'
      });
    }
  },

  async loadCompanies() {
    try {
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('未找到token，等待app.ts中的登录完成');
        this.setData({ companiesLoading: false });
        return;
      }

      // 设置loading状态
      this.setData({ companiesLoading: true });

      const res = await request({
        url: `${CURRENT_API_BASE_URL}/company/with-count`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<Company[]>;
      if (code !== 0) {
        throw new Error(msg || '获取公司列表失败');
      }

      // 更新缓存
      wx.setStorageSync('companies_cache', data);

      this.setData({
        companies: data,
        companiesLoaded: true,
        companiesLoading: false
      });

      console.log('公司数据加载完成并缓存');
    } catch (error) {
      console.error('获取公司列表失败:', error);
      this.setData({ companiesLoading: false });
      wx.showToast({
        title: '加载公司分类失败',
        icon: 'none'
      });
    }
  },

  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },

  async loadData(): Promise<void> {
    console.log('开始加载业务数据');
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未找到token，跳过数据加载');
      return;
    }

    if (this.data.loading) {
      console.log('正在加载中，跳过请求');
      return;
    }

    // 只有 questions 为空时才请求接口
    if (this.data.questions.length > 0) {
      console.log('questions 已有内容，跳过请求');
      return;
    }

    // 显示骨架屏
    this.setData({ loading: true });
    try {
      // 不要重置 questions
      this.setData({
        page: 1,
        hasMore: true
      });
      console.log('开始请求题目列表');
      await this.fetchQuestions();
      console.log('题目列表请求完成');
      // 如果分类数据未加载，则加载所有分类数据
      if (!this.data.pmTypesLoaded || !this.data.companiesLoaded) {
        console.log('开始加载分类数据');
        const promises = [];
        if (!this.data.pmTypesLoaded) {
          console.log('加载PM类型数据');
          promises.push(this.loadPMTypes());
        }
        if (!this.data.companiesLoaded) {
          console.log('加载公司数据');
          promises.push(this.loadCompanies());
        }
        await Promise.all(promises);
        console.log('分类数据加载完成');
      }
    } catch (error: any) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: error && error.message ? error.message : '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      // 隐藏骨架屏
      this.setData({ loading: false });
    }
  },

  onShareAppMessage() {
    return {
      title: '荔枝刷题-学产品，上荔枝',
      path: '/pages/question-bank/index',
      imageUrl: '/images/other/wechatShare_nowater_zip.jpg' // 如有自定义分享图片可解注释
    };
  },

  async preloadQuestionData(questionId: string) {
    // 检查是否已有缓存
    const cache = getQuestionCache(questionId);
    if (cache) {
      console.log('Question already cached:', questionId);
      return cache;
    }

    // 如果没有缓存，预加载数据
    try {
      console.log('Preloading question data:', questionId);
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions/${questionId}`,
        method: 'GET'
      });

      if (res && res.code === 0 && res.data) {
        const questionData = res.data;
        setQuestionCache(questionId, questionData);
        console.log('Question data preloaded and cached:', questionId);
        return questionData;
      }
    } catch (e) {
      console.error('Failed to preload question data:', e);
    }
    return null;
  },

  async onQuestionTap(e: any) {
    console.log('onQuestionTap event:', e);

    // 优先使用组件传递的参数
    if (e.detail) {
      const { index, id, questionIds } = e.detail;
      console.log('onQuestionTap from component:', { index, id, questionIds });

      // 验证参数有效性
      if (typeof index !== 'number' || index < 0) {
        console.error('Invalid index:', index);
        return;
      }

      // 使用组件传递的questionIds，如果没有则使用当前页面的questionIds
      const finalQuestionIds = questionIds && questionIds.length > 0 ? questionIds : this.data.questionIds;
      const finalId = id || finalQuestionIds[index];

      if (!finalId) {
        console.error('No valid question ID found');
        return;
      }

      console.log('onQuestionTap final params:', {
        index,
        id: finalId,
        questionIds: finalQuestionIds
      });

      // 预加载当前题目和相邻题目的数据
      const preloadPromises = [];

      // 预加载当前题目
      preloadPromises.push(this.preloadQuestionData(finalId));

      // 预加载前一题
      if (index > 0 && finalQuestionIds[index - 1]) {
        preloadPromises.push(this.preloadQuestionData(finalQuestionIds[index - 1]));
      }

      // 预加载后一题
      if (index < finalQuestionIds.length - 1 && finalQuestionIds[index + 1]) {
        preloadPromises.push(this.preloadQuestionData(finalQuestionIds[index + 1]));
      }

      // 启动预加载（不等待完成）
      Promise.all(preloadPromises).then(() => {
        console.log('Question data preloading completed');
      }).catch(err => {
        console.error('Question data preloading failed:', err);
      });

      const url = `/pages/question-detail/index?id=${finalId}&index=${index}&questionIds=${encodeURIComponent(JSON.stringify(finalQuestionIds))}`;
      console.log('navigating to URL:', url);

      wx.navigateTo({
        url: url
      });
      return;
    }

    // 兼容原生事件（备用方案）
    const index = e.currentTarget && e.currentTarget.dataset.index;
    const questions = this.data.questions || [];
    const questionIds = questions.map((q: any) => q.id);
    const id = questionIds[index];
    console.log('onQuestionTap fallback:', {index, questions, questionIds, id});

    // 预加载数据
    if (id) {
      this.preloadQuestionData(id);
    }

    wx.navigateTo({
      url: `/pages/question-detail/index?id=${id}&index=${index}&questionIds=${encodeURIComponent(JSON.stringify(questionIds))}`
    });
  }
});