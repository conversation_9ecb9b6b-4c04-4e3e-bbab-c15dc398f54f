import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse, PaginatedResponse } from '../../types/api';
import { trackEvent } from 'umtrack-wx';
const { request } = require('../../utils/request');

interface Category {
  id: string;
  name: string;
  icon?: string;
  count: number;
}

interface Question {
  id: string;
  title: string;
  tags: string[];
  description: string;
  answer: string;
  keyPoints: string[];
  views: number;
  likes: number;
  isLiked: boolean;
  isCollected: boolean;
  pmTypeName: string;
  companyName: string;
  category: string[];
}

interface PMType {
  id: string;
  name: string;
  questionCount: number;
}

interface Company {
  id: string;
  name: string;
  questionCount: number;
}

// 全局缓存变量
let allQuestionsCache: {
  questions: Question[];
  searchQuery: string;
  pmTypeId: string;
  companyId: string;
} | null = null;

Page({
  data: {
    currentTab: 'all',
    scrollLeft: 0,
    questions: [] as Question[],
    categories: [] as Category[],
    pmTypes: [] as Array<{id: string; name: string; questionCount: number; displayName: string}>,
    companies: [] as Array<{id: string; name: string; questionCount: number}>,
    searchQuery: '',
    loading: false,
    page: 1,
    hasMore: true,
    pmTypeId: '',
    currentPmTypeDisplayName: '',
    companyId: '',
    currentCompanyName: '',
    pmTypesLoaded: false,
    companiesLoaded: false,
    pmType: '',
    company: '',
    allQuestionsCache: null as {
      questions: Question[];
      searchQuery: string;
      pmTypeId: string;
      companyId: string;
    } | null,
    questionIds: [] as string[]
  },

  onLoad() {
    // 优先读取本地缓存
    const listCache = wx.getStorageSync('question_list_cache');
    if (listCache && Array.isArray(listCache.questions)) {
      this.setData({
        questions: listCache.questions,
        questionIds: listCache.questions.map(q => q.id),
        // 其它相关字段可按需补充
      });
    }
    // 分类数据如果没加载，依然要请求
    if (!this.data.pmTypesLoaded) {
      this.loadPMTypes();
    }
    if (!this.data.companiesLoaded) {
      this.loadCompanies();
    }
    console.log('页面加载');
    this.initData();
  },

  onReady() {
    // 页面渲染完成后，后台刷新列表
    this.fetchQuestionListAndUpdateCache();
  },

  async fetchQuestionListAndUpdateCache() {
    // 只在全部tab第一页且无筛选/搜索时刷新缓存
    if (
      this.data.currentTab === 'all' &&
      this.data.page === 1 &&
      !this.data.searchQuery &&
      !this.data.pmTypeId &&
      !this.data.companyId
    ) {
      try {
        const res = await request({
          url: `${CURRENT_API_BASE_URL}/questions`,
          method: 'GET',
          data: { page: 1, limit: 25 }
        });
        if (res && res.code === 0 && Array.isArray(res.data.list)) {
          this.setData({ 
            questions: res.data.list,
            questionIds: res.data.list.map(q => q.id),
          });
          wx.setStorageSync('question_list_cache', {
            questions: res.data.list,
            searchQuery: '',
            pmTypeId: '',
            companyId: ''
          });
        }
      } catch (e) {
        // 忽略异常
      }
    }
  },

  async initData() {
    console.log('初始化数据');
    const token = wx.getStorageSync('token');
    console.log('当前token:', token);
    
    if (token) {
      console.log('已有token，直接加载数据');
      console.log('走到这儿bbbbbb');
      await this.loadData();
    } else {
      console.log('未找到token，等待app.ts中的登录完成');
      const app = getApp<IAppOption>();
      try {
        await app.checkLogin();
        console.log('登录状态检查完成，开始加载数据');
        console.log('走到这儿ccccccc');
        await this.loadData();
      } catch (error) {
        console.error('登录失败:', error);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    }
  },

  onPullDownRefresh() {
    wx.showNavigationBarLoading();
    const resetData: any = {
      questions: [],
      page: 1,
      hasMore: true,
      pmTypesLoaded: false,
      companiesLoaded: false
    };
    // 只在全部tab下清空缓存
    if (this.data.currentTab === 'all') {
      console.log('清空全局全部题库缓存');
      allQuestionsCache = null;
      resetData.allQuestionsCache = null;
    }
    this.setData(resetData, async () => {
      console.log('走到这儿dddddd');
      await this.loadData();
      wx.hideNavigationBarLoading();
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    console.log('触底加载更多');
    if (this.data.hasMore && !this.data.loading) {
      this.fetchQuestions();
    }
  },

  fetchQuestions(): Promise<void> {
    // 新增逻辑：如果是在分类Tab (direction 或 company) 且没有选择具体分类ID (pmType/company)，则不加载题目。
    if ((this.data.currentTab === 'direction' && !this.data.pmType) || 
        (this.data.currentTab === 'company' && !this.data.company)) {
      this.setData({
        questions: [],
        page: 1,
        hasMore: false, // 没有加载，所以没有更多
        loading: false
      });
      console.log(`[fetchQuestions] Tab: ${this.data.currentTab}, 未选择具体分类 (pmType: ${this.data.pmType}, company: ${this.data.company})，不加载题目列表。`);
      return Promise.resolve(); // 直接返回，不执行后续请求
    }

    console.log('[fetchQuestions] 开始请求题目列表，页码:', this.data.page);
    
    return new Promise<void>((resolve, reject) => {
      // 构建请求参数
      const requestData: any = {
        page: this.data.page,
        limit: 25
      };
      
      // 只有当tab不是'all'时才添加tab参数
      if (this.data.currentTab !== 'all') {
        requestData.tab = this.data.currentTab;
      }
      
      // 只有当searchQuery不为空时才添加search参数
      if (this.data.searchQuery) {
        requestData.search = this.data.searchQuery;
      }

      // 如果当前有选中的PM类型，添加pmTypeId参数
      if (this.data.pmTypeId) {
        requestData.pmTypeId = this.data.pmTypeId;
      }

      // 如果当前有选中的公司，添加companyId参数
      if (this.data.companyId) {
        requestData.companyId = this.data.companyId;
      }
      
      console.log('[fetchQuestions] 请求参数:', requestData);
      
      const makeRequest = async () => {
        try {
          const res = await request({
            url: `${CURRENT_API_BASE_URL}/questions`,
            method: 'GET',
            data: requestData,
          });

          console.log('[fetchQuestions] 请求成功，返回数据:', res.data);
          const { code, data, msg } = res as PaginatedResponse<Question>;
          
          if (code !== 0) {
            wx.showToast({
              title: msg || '获取题目失败',
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(new Error(msg));
            return;
          }
          
          const newQuestions = data.list.map((item: Question) => ({
            id: item.id,
            title: item.title,
            tags: item.tags || [],
            description: item.description || '',
            answer: item.answer || '',
            keyPoints: item.keyPoints || [],
            views: item.views || 0,
            likes: item.likes || 0,
            isLiked: item.isLiked || false,
            isCollected: item.isCollected || false,
            pmTypeName: item.pmTypeName || '',
            companyName: item.companyName || '',
            category: item.category || []
          }));
          
          
          this.setData({
            questions: [...this.data.questions, ...newQuestions],
            questionIds: [...(this.data.questionIds || []), ...newQuestions.map(q => q.id)],
            page: this.data.page + 1,
            hasMore: newQuestions.length === 25,
            loading: false
          });
          // 只在全部tab第一页且无搜索/筛选时写入本地缓存
          if (
            this.data.currentTab === 'all' &&
            requestData.page === 1 &&
            !this.data.searchQuery &&
            !this.data.pmTypeId &&
            !this.data.companyId
          ) {
            allQuestionsCache = {
              questions: [...newQuestions],
              searchQuery: '',
              pmTypeId: '',
              companyId: ''
            };

            wx.setStorageSync('question_list_cache', {
              questions: [...newQuestions],
              searchQuery: '',
              pmTypeId: '',
              companyId: ''
            });

            this.setData({ allQuestionsCache }); // 仅用于调试
          }
          resolve();
        } catch (err: any) {
          console.error('获取题目列表失败', err);
          
          // 如果是401错误，尝试重新登录并重试请求
          if (err.statusCode === 401) {
            console.log('Token已过期，尝试重新登录');
            // 优先展示后端返回的msg
            let msg = '登录已过期，请重试';
            if (err.data && err.data.msg) {
              msg = err.data.msg;
            }
            wx.showToast({
              title: msg,
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(err);
          } else {
            wx.showToast({
              title: err.message || '获取题目失败',
              icon: 'none'
            });
            this.setData({ loading: false });
            reject(err);
          }
        }
      };

      makeRequest().catch(reject);
    });
  },

  onScroll(e: any) {
    this.setData({
      scrollLeft: e.detail.scrollLeft
    });
  },

  switchTab(e: any) {
    const newTab = e.currentTarget.dataset.tab;
    if (this.data.currentTab === newTab) return;

    trackEvent('top_tab_click', { tabName: newTab });

    // 如果切到全部tab，优先读取缓存
    if (newTab === 'all') {
      if (allQuestionsCache && allQuestionsCache.questions && allQuestionsCache.questions.length > 0) {
        this.setData({
          currentTab: newTab,
          questions: allQuestionsCache.questions,
          page: 2, // 缓存的是第一页，所以下一页应为2
          hasMore: true,
          searchQuery: '',
          pmTypeId: '',
          pmType: '',
          currentPmTypeDisplayName: '',
          companyId: '',
          company: '',
          currentCompanyName: ''
        }, () => {
          // 滚动位置逻辑保留
          const query = wx.createSelectorQuery();
          query.select(`#tab-${newTab}`).boundingClientRect();
          query.select('.tab-scroll').boundingClientRect();
          query.exec((res) => {
            if (res && res[0] && res[1]) {
              const tabRect = res[0];
              const scrollRect = res[1];
              if (tabRect && scrollRect) {
                const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
                this.setData({ scrollLeft });
              }
            }
          });
        });
        return;
      }
      // 没有缓存才请求接口
      this.setData({
        currentTab: newTab,
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        pmTypeId: '',
        pmType: '',
        currentPmTypeDisplayName: '',
        companyId: '',
        company: '',
        currentCompanyName: ''
      }, () => {
        this.fetchQuestions();
        const query = wx.createSelectorQuery();
        query.select(`#tab-${newTab}`).boundingClientRect();
        query.select('.tab-scroll').boundingClientRect();
        query.exec((res) => {
          if (res && res[0] && res[1]) {
            const tabRect = res[0];
            const scrollRect = res[1];
            if (tabRect && scrollRect) {
              const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
              this.setData({ scrollLeft });
            }
          }
        });
      });
      return;
    }

    // 切到方向/公司tab，只切tab，不请求题目列表
    if (newTab === 'direction' || newTab === 'company') {
      this.setData({ currentTab: newTab });
      // 滚动位置逻辑保留
      const query = wx.createSelectorQuery();
      query.select(`#tab-${newTab}`).boundingClientRect();
      query.select('.tab-scroll').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const tabRect = res[0];
          const scrollRect = res[1];
          if (tabRect && scrollRect) {
            const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
            this.setData({ scrollLeft });
          }
        }
      });
      return;
    }


    // 其他tab逻辑保持不变
    this.setData({
      currentTab: newTab,
      questions: [],
      page: 1,
      hasMore: true,
      searchQuery: '',
      pmTypeId: '',
      pmType: '',
      currentPmTypeDisplayName: '',
      companyId: '',
      company: '',
      currentCompanyName: ''
    }, () => {
      this.fetchQuestions();
      const query = wx.createSelectorQuery();
      query.select(`#tab-${newTab}`).boundingClientRect();
      query.select('.tab-scroll').boundingClientRect();
      query.exec((res) => {
        if (res && res[0] && res[1]) {
          const tabRect = res[0];
          const scrollRect = res[1];
          if (tabRect && scrollRect) {
            const scrollLeft = tabRect.left - scrollRect.left + (tabRect.width - scrollRect.width) / 2;
            this.setData({ scrollLeft });
          }
        }
      });
    });
  },

  onSearch(e: WechatMiniprogram.Input) {
    const value = e.detail.value;
    console.log('搜索关键词:', value);
    this.setData({ 
      searchQuery: value,
      questions: [],
      page: 1,
      hasMore: true
    }, () => {
      this.fetchQuestions();
    });
  },


  async toggleCollect(e: WechatMiniprogram.TouchEvent) {
    const id = e.currentTarget.dataset.id;
    console.log('切换收藏状态, ID:', id);
    const questions = this.data.questions.map(q => {
      if (q.id === id) {
        return { ...q, isCollected: !q.isCollected };
      }
      return q;
    });
    this.setData({ questions });
    // 调用收藏/取消收藏接口
    const question = this.data.questions.find(q => q.id === id);
    if (question) {
      console.log('发送收藏请求, ID:', id, '当前状态:', question.isCollected);
      try {
        const res = await request({
          url: `${CURRENT_API_BASE_URL}/questions/${id}/collect`,
          method: question.isCollected ? 'POST' : 'DELETE',
        });
        const { code, msg } = res as ApiResponse;
        if (code !== 0) {
          wx.showToast({
            title: msg || '操作失败',
            icon: 'none'
          });
          return;
        }
        console.log('收藏状态更新成功', res);
      } catch (err) {
        console.error('收藏状态更新失败', err);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    }
  },

  goToCategory(e: WechatMiniprogram.TouchEvent) {
    const { id, name } = e.currentTarget.dataset;
    console.log('[goToCategory] 点击分类', { id, name, currentTab: this.data.currentTab });
    if (this.data.currentTab === 'direction') {
      const selectedType = this.data.pmTypes.find(type => type.id === id);
      this.setData({
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        pmTypeId: id,
        pmType: id,
        currentPmTypeDisplayName: selectedType?.displayName || name || '',
        companyId: '',
        company: '',
        currentCompanyName: ''
      }, () => {
        console.log('[goToCategory] direction setData后 pmTypeId:', this.data.pmTypeId);
        this.fetchQuestions();
      });
    } else if (this.data.currentTab === 'company') {
      this.setData({
        questions: [],
        page: 1,
        hasMore: true,
        searchQuery: '',
        companyId: id,
        company: id,
        currentCompanyName: name,
        pmTypeId: '',
        pmType: '',
        currentPmTypeDisplayName: ''
      }, () => {
        console.log('[goToCategory] company setData后 companyId:', this.data.companyId);
        this.fetchQuestions();
      });
    }
  },

  goBackToCategories() {
    this.setData({
      pmTypeId: '',
      pmType: '',
      currentPmTypeDisplayName: '',
      companyId: '',
      company: '',
      currentCompanyName: '',
      questions: [],
      page: 1,
      hasMore: true
    }, () => {
      this.fetchQuestions();
    });
  },

  async loadPMTypes() {
    try {
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('未找到token，等待app.ts中的登录完成');
        return;
      }
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/pm-type/with-count`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<PMType[]>;
      if (code !== 0) {
        throw new Error(msg || '获取PM类型失败');
      }
      this.setData({
        pmTypes: data.map(item => ({
          ...item,
          displayName: item.name.replace(/经理$/, '')
        })),
        pmTypesLoaded: true
      });
    } catch (error) {
      console.error('获取PM类型失败:', error);
      wx.showToast({
        title: '加载方向分类失败',
        icon: 'none'
      });
    }
  },

  async loadCompanies() {
    try {
      const token = wx.getStorageSync('token');
      if (!token) {
        console.log('未找到token，等待app.ts中的登录完成');
        return;
      }
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/company/with-count`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<Company[]>;
      if (code !== 0) {
        throw new Error(msg || '获取公司列表失败');
      }
      this.setData({
        companies: data,
        companiesLoaded: true
      });
    } catch (error) {
      console.error('获取公司列表失败:', error);
      wx.showToast({
        title: '加载公司分类失败',
        icon: 'none'
      });
    }
  },

  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/index'
    });
  },

  async loadData(): Promise<void> {
    console.log('开始加载业务数据');
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未找到token，跳过数据加载');
      return;
    }

    if (this.data.loading) {
      console.log('正在加载中，跳过请求');
      return;
    }

    // 只有 questions 为空时才请求接口
    if (this.data.questions.length > 0) {
      console.log('questions 已有内容，跳过请求');
      return;
    }

    // 显示骨架屏
    this.setData({ loading: true });
    try {
      // 不要重置 questions
      this.setData({
        page: 1,
        hasMore: true
      });
      console.log('开始请求题目列表');  
      await this.fetchQuestions();
      console.log('题目列表请求完成');
      // 如果分类数据未加载，则加载所有分类数据
      if (!this.data.pmTypesLoaded || !this.data.companiesLoaded) {
        console.log('开始加载分类数据');
        const promises = [];
        if (!this.data.pmTypesLoaded) {
          console.log('加载PM类型数据');
          promises.push(this.loadPMTypes());
        }
        if (!this.data.companiesLoaded) {
          console.log('加载公司数据');
          promises.push(this.loadCompanies());
        }
        await Promise.all(promises);
        console.log('分类数据加载完成');
      }
    } catch (error: any) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: error && error.message ? error.message : '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      // 隐藏骨架屏
      this.setData({ loading: false });
    }
  },

  onShareAppMessage() {
    return {
      title: '荔枝刷题-学产品，上荔枝',
      path: '/pages/question-bank/index',
      imageUrl: '/images/other/wechatShare_nowater_zip.jpg' // 如有自定义分享图片可解注释
    };
  },

  onQuestionTap(e: any) {
    // 兼容组件触发和原生事件
    const index = (e.detail && typeof e.detail.index === 'number') ? e.detail.index : (e.currentTarget && e.currentTarget.dataset.index);
    const questions = this.data.questions || [];
    const questionIds = questions.map((q: any) => q.id);
    const id = questionIds[index];
    console.log('onQuestionTap', {index, questions, questionIds, id});
    wx.navigateTo({
      url: `/pages/question-detail/index?id=${id}&index=${index}&questionIds=${encodeURIComponent(JSON.stringify(questionIds))}`
    });
  }
}); 