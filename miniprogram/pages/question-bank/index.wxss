/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a80 50%, #ffab91 100%);
  box-shadow: 0 2rpx 16rpx rgba(255, 107, 157, 0.2);
}

.status-bar {
  width: 100%;
}

.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
}

.nav-left {
  flex: 1;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  opacity: 1;
  transform: translateX(0);
}

.nav-left-hidden {
  opacity: 0;
  transform: translateX(-20rpx);
}

.nav-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

.nav-center-show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.center-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
  letter-spacing: 2rpx;
}

.brand-text {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.brand-main {
  font-size: 36rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.brand-separator {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 2rpx;
}

.brand-sub {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 1rpx;
}

.nav-right {
  display: flex;
  align-items: center;
}

.search-btn {
  width: 68rpx;
  height: 68rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.15);
}

.search-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1rpx 4rpx rgba(255, 107, 157, 0.2);
}

.search-btn .search-icon {
  width: 36rpx;
  height: 36rpx;
  filter: brightness(0) invert(1);
}

.container {
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(255, 107, 157, 0.03) 0%, #f8f9fa 200rpx);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.search-box {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  background: linear-gradient(135deg, rgba(255, 107, 157, 0.1) 0%, rgba(255, 138, 128, 0.08) 100%);
  border-radius: 24rpx;
  margin-top: -10rpx;
  border: 1rpx solid rgba(255, 107, 157, 0.15);
  backdrop-filter: blur(20rpx);
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 搜索框吸顶状态 */
.search-box-sticky {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 0;
  margin: 0;
  padding: 20rpx 30rpx;
  border: none;
  backdrop-filter: blur(15rpx);
}

.sticky-container {
  position: sticky;
  top: 0;
  z-index: 99;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
  width: 100%;
  left: 0;
  right: 0;
  padding: 0 15px;
  box-sizing: border-box;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 107, 157, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 157, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 吸顶状态样式 */
.sticky-container-active {
  background: linear-gradient(135deg, #ff6b9d 0%, #ff8a80 50%, #ffab91 100%) !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100vw !important;
  left: -15px !important;
  border: none !important;
  box-shadow: 0 2rpx 16rpx rgba(255, 107, 157, 0.2) !important;
}

.search-box input {
  width: 100%;
  height: 80rpx;
  border: none;
  border-radius: 16rpx;
  padding: 0 32rpx 0 80rpx;
  background: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  box-sizing: border-box;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.search-box .iconfont {
  position: absolute;
  left: 56rpx;
  z-index: 2;
  font-size: 32rpx;
  color: rgba(255, 107, 157, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 吸顶状态下的搜索框内部样式 */
.search-box-sticky input {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
}

.search-box-sticky .iconfont {
  color: rgba(255, 255, 255, 0.8);
}

.tab-scroll {
  width: 100%;
  white-space: nowrap;
  background: #fff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tab-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

.content {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #f7f8fa;
  padding: 24rpx 0;
  margin-bottom: 24rpx;
}

.search-input {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.placeholder {
  color: #999;
}

.tabs {
  width: 100%;
  background: #fff;
  padding: 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 吸顶状态下的标签样式 */
.sticky-container-active .tabs {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  backdrop-filter: blur(10rpx);
}

.sticky-container-active .tab {
  color: rgba(255, 255, 255, 0.8) !important;
}

.sticky-container-active .tab.active {
  color: #ffffff !important;
}

.sticky-container-active .tab.active::after {
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%) !important;
}

.tab-wrapper {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  padding: 0 20rpx;
}

.tab {
  flex-shrink: 0;
  text-align: center;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  font-weight: 500;
  color: #ff6b9d;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #ff6b9d 0%, #ff8a80 100%);
  border-radius: 2rpx;
}

.question-list {
  padding: 0 0 32rpx 0;
}

.question-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  width: 100%;
  word-break: break-all;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
  width: 100%;
}

.tag {
  font-size: 24rpx;
  color: #666;
  background: #f5f6f7;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
  white-space: nowrap;
}

.question-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
  width: 100%;
  word-break: break-all;
}

.question-stats {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  width: 100%;
}

.stat {
  margin-right: 30rpx;
}

.collect {
  margin-left: auto;
  color: #4080ff;
}

.loading {
  text-align: center;
  padding: 32rpx 0;
  color: #999;
  font-size: 26rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 32rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 分类列表样式 */
.category-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 30rpx;

  background: #fff;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.category-card {
  background: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.category-card:active {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
  transform: translateY(2rpx);
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}

.category-info {
  width: 100%;
}

.category-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.list-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.back-btn .icon-back {
  font-size: 36rpx;
  color: #333;
}

.list-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-list {
  padding: 30rpx 20rpx;
}

.skeleton-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.skeleton-title {
  width: 85%;
  height: 40rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 32rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-desc {
  width: 100%;
  height: 80rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-stats {
  width: 100rpx;
  height: 32rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

/* 分类loading样式 */
.category-loading {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 30rpx;
  background: #fff;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.category-skeleton {
  background: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.skeleton-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skeleton-name {
  width: 80%;
  height: 28rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4rpx;
}

.skeleton-count {
  width: 60%;
  height: 24rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4rpx;
}