.container {
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  background: #f5f6f7;
  min-height: fit-content;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  background-color: #f5f5f5;
}

.search-box {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
}

.sticky-container {
  position: sticky;
  top: 0;
  z-index: 99;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 10rpx;
  width: 100%;
  left: 0;
  right: 0;
  padding: 0 15px;
  box-sizing: border-box;
}

.search-box input {
  width: 100%;
  height: 40px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0 10px;
  background: #f5f6f7;
  font-size: 28rpx;
  box-sizing: border-box;
}

.tab-scroll {
  width: 100%;
  white-space: nowrap;
  background: #fff;
  padding: 20rpx 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tab-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #4080ff;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

.content {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.search-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #f7f8fa;
  padding: 24rpx 0;
  margin-bottom: 24rpx;
}

.search-input {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx 32rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.placeholder {
  color: #999;
}

.tabs {
  width: 100%;
  background: #fff;
  padding: 0;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-wrapper {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
  padding: 0 20rpx;
}

.tab {
  flex-shrink: 0;
  text-align: center;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab.active {
  font-weight: 500;
  color: #4080ff;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #4080ff;
  border-radius: 2rpx;
}

.question-list {
  padding: 0 0 32rpx 0;
}

.question-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  width: 100%;
  word-break: break-all;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
  width: 100%;
}

.tag {
  font-size: 24rpx;
  color: #666;
  background: #f5f6f7;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
  white-space: nowrap;
}

.question-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  line-height: 1.5;
  width: 100%;
  word-break: break-all;
}

.question-stats {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  width: 100%;
}

.stat {
  margin-right: 30rpx;
}

.collect {
  margin-left: auto;
  color: #4080ff;
}

.loading {
  text-align: center;
  padding: 32rpx 0;
  color: #999;
  font-size: 26rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 32rpx 0;
  color: #999;
  font-size: 26rpx;
}

/* 分类列表样式 */
.category-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 30rpx;

  background: #fff;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.category-card {
  background: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.category-card:active {
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
  transform: translateY(2rpx);
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
}

.category-info {
  width: 100%;
}

.category-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-count {
  font-size: 24rpx;
  color: #999;
}

.list-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 36rpx;
  height: 36rpx;
}

.back-btn .icon-back {
  font-size: 36rpx;
  color: #333;
}

.list-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-list {
  padding: 30rpx 20rpx;
}

.skeleton-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.skeleton-title {
  width: 85%;
  height: 40rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 32rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-desc {
  width: 100%;
  height: 80rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

.skeleton-stats {
  width: 100rpx;
  height: 32rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 6rpx;
}

/* 分类loading样式 */
.category-loading {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  padding: 30rpx;
  background: #fff;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.category-skeleton {
  background: #fff;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.skeleton-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  border-radius: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-info {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skeleton-name {
  width: 80%;
  height: 28rpx;
  margin-bottom: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4rpx;
}

.skeleton-count {
  width: 60%;
  height: 24rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4rpx;
}