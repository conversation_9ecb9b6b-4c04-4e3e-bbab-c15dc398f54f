<!-- 题库页面 -->
<view class="container">
  <!-- 顶部搜索框 -->
  <view class="search-box" bindtap="goToSearch">
    <text class="iconfont icon-search"></text>
    <input type="text" placeholder="搜索题目、公司或方向..." placeholder-class="placeholder" disabled/>
  </view>

  <!-- 吸顶容器 -->
  <view class="sticky-container">
    <!-- 分类标签 -->
    <view class="tabs" wx:if="{{!pmTypeId && !companyId}}">
      <scroll-view
        class="tab-scroll"
        scroll-x
        scroll-with-animation
        scroll-left="{{scrollLeft}}"
        bindscroll="onScroll"
      >
        <view class="tab-wrapper">
          <view
            class="tab {{currentTab === 'all' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="all"
            id="tab-all"
          >全部题库</view>
          <view
            class="tab {{currentTab === 'direction' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="direction"
            id="tab-direction"
          >方向分类</view>
          <view
            class="tab {{currentTab === 'company' ? 'active' : ''}}"
            bindtap="switchTab"
            data-tab="company"
            id="tab-company"
          >公司分类</view>
        </view>
      </scroll-view>
    </view>
    <!-- 题目列表标题 -->
    <view class="list-header" wx:if="{{pmTypeId || companyId}}">
      <view class="back-btn" bindtap="goBackToCategories">
        <image src="/images/back.png" mode="aspectFit" class="back-icon"></image>
      </view>
      <view class="list-title">{{pmTypeId ? currentPmTypeDisplayName : currentCompanyName}}的题目</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content">
    <!-- 骨架屏 -->
    <view wx:if="{{loading && questions.length === 0}}" class="skeleton-list">
      <view class="skeleton-card" wx:for="{{[1,2,3,4,5]}}" wx:key="*this">
        <view class="skeleton-title"></view>
        <view class="skeleton-tags">
          <view class="skeleton-tag"></view>
          <view class="skeleton-tag"></view>
        </view>
        <view class="skeleton-desc"></view>
        <view class="skeleton-stats"></view>
      </view>
    </view>

    <!-- 分类列表 -->
    <view class="category-list" wx:if="{{currentTab === 'direction' && !pmType}}">
      <!-- PM类型loading -->
      <block wx:if="{{pmTypesLoading}}">
        <view wx:for="{{[1,2,3,4,5,6]}}" wx:key="*this" class="category-skeleton">
          <view class="skeleton-icon"></view>
          <view class="skeleton-info">
            <view class="skeleton-name"></view>
            <view class="skeleton-count"></view>
          </view>
        </view>
      </block>

      <!-- PM类型数据 -->
      <block wx:else>
        <view wx:for="{{pmTypes}}" wx:key="id" class="category-card" bindtap="goToCategory" data-id="{{item.id}}" data-name="{{item.name}}">
          <image class="category-icon" src="/images/pmtype/{{item.name}}.svg" mode="aspectFit"></image>
          <view class="category-info">
            <view class="category-name">{{item.displayName}}</view>
            <view class="category-count">{{item.questionCount}}题</view>
          </view>
        </view>
      </block>
    </view>

    <view class="category-list" wx:if="{{currentTab === 'company' && !company}}">
      <!-- 公司loading -->
      <block wx:if="{{companiesLoading}}">
        <view wx:for="{{[1,2,3,4,5,6]}}" wx:key="*this" class="category-skeleton">
          <view class="skeleton-icon"></view>
          <view class="skeleton-info">
            <view class="skeleton-name"></view>
            <view class="skeleton-count"></view>
          </view>
        </view>
      </block>

      <!-- 公司数据 -->
      <block wx:else>
        <view wx:for="{{companies}}" wx:key="id" class="category-card" bindtap="goToCategory" data-id="{{item.id}}" data-name="{{item.name}}">
          <image class="category-icon" src="/images/company/{{item.name}}.svg" mode="aspectFit"></image>
          <view class="category-info">
            <view class="category-name">{{item.name}}</view>
            <view class="category-count">{{item.questionCount}}题</view>
          </view>
        </view>
      </block>
    </view>

    <!-- 题目列表 -->
    <view class="question-list" wx:if="{{currentTab === 'all' || currentTab === 'field' || (currentTab === 'direction' && pmTypeId) || (currentTab === 'company' && companyId)}}">
      <question-item
        wx:for="{{questions}}"
        wx:key="id-{{item.id}}"
        question="{{item}}"
        index="{{index}}"
        questionIds="{{questionIds}}"
        bindtap="onQuestionTap"
      />
      <!-- 加载更多提示 -->
      <view wx:if="{{loading && questions.length > 0}}" class="loading-more">
        <text>加载中...</text>
      </view>
      <view wx:if="{{!hasMore && questions.length > 0}}" class="no-more">
        <text>没有更多了</text>
      </view>
    </view>
  </view>
</view>

<favorite-guide />

