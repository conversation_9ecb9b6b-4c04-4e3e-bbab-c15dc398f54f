<wxs module="utils">
  // 获取公司图标路径
  function getCompanyIconPath(companyName) {
    if (!companyName) return '';

    // 公司名称映射到文件名
    var companyIconMap = {
      '阿里巴巴': '/images/company/阿里巴巴.svg',
      '腾讯': '/images/company/腾讯.svg',
      '字节跳动': '/images/company/字节跳动.svg',
      '美团': '/images/company/美团.svg',
      '百度': '/images/company/百度.svg',
      '京东': '/images/company/京东.svg',
      '网易': '/images/company/网易.svg',
      '快手': '/images/company/快手.svg',
      '滴滴': '/images/company/滴滴.svg'
    };

    return companyIconMap[companyName] || '';
  }

  module.exports = {
    getCompanyIconPath: getCompanyIconPath
  };
</wxs>

<view class="container">
  <block wx:if="{{loading}}">
    <!-- 骨架屏 -->
    <view class="skeleton-card">
      <view class="skeleton-title">
        <view class="skeleton-title-inner"></view>
      </view>
      <view class="skeleton-tags">
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
      </view>
    </view>
    <view class="skeleton-content">
      <view class="skeleton-section">
        <view class="skeleton-section-title"></view>
        <view class="skeleton-section-content"></view>
      </view>
      <view class="skeleton-section">
        <view class="skeleton-section-title"></view>
        <view class="skeleton-section-content"></view>
      </view>
    </view>
  </block>

  <block wx:else>
    <swiper
      style="height: 100vh;"
      vertical
      current="{{swiperCurrent}}"
      bindchange="onSwiperChange"
      bindtransition="onSwiperTransition"
      bindanimationfinish="onSwiperAnimationFinish"
      easing-function="easeOutQuart"
      duration="400"
      indicator-dots="{{!singleMode}}"
      indicator-color="rgba(255,255,255,0.3)"
      indicator-active-color="rgba(64,158,255,0.8)"
      circular="{{!singleMode}}"
      disable-touch="{{singleMode}}"
    >
      <swiper-item wx:for="{{swiperQuestions}}" wx:key="uniqueKey" wx:for-item="item" wx:for-index="idx">
        <scroll-view scroll-y="true" style="height: 100vh;" scroll-with-animation="true">
          <view class="swiper-question" style="{{swiperItemStyles[idx] || ''}}">
            <view class="title-card">
              <!-- 优雅的加载指示器 -->
              <view class="loading-indicator" wx:if="{{!item.title}}">
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
                <view class="loading-dot"></view>
              </view>

              <!-- 公司图标 -->
              <view class="company-icon-container" data-company="{{item.companyName}}" wx:if="{{item.companyName && item.title}}">
                <image
                  class="company-icon"
                  src="{{utils.getCompanyIconPath(item.companyName)}}"
                  mode="aspectFit"
                  wx:if="{{utils.getCompanyIconPath(item.companyName)}}"
                  binderror="onCompanyIconError"
                  bindload="onCompanyIconLoad"
                />
                <view class="company-name" data-company="{{item.companyName}}" wx:else>{{item.companyName}}</view>
              </view>

              <view class="title" wx:if="{{item.title}}">{{item.title}}</view>
              <view class="tags" wx:if="{{item.tags && item.tags.length > 0}}">
                <view wx:for="{{item.tags}}" wx:key="*this" class="tag">{{item}}</view>
              </view>
            </view>
            <view class="knowledge-card">
              <view class="section-title">相关知识点</view>
              <view class="section-content">
                <view wx:for="{{item.keyPoints}}" wx:key="*this" class="key-point">• {{item}}</view>
              </view>
            </view>
            <view class="answer-card">
              <view class="section-title answer-title">
                参考答案
              </view>
              <view class="section-content">
                <block wx:if="{{item.answerLoading}}">
                  <view class="answer-loading">答案加载中...</view>
                </block>
                <block wx:elif="{{item.answerHtml}}">
                  <rich-text nodes="{{item.answerHtml}}" />
                </block>
                <block wx:else>
                  <view class="empty-answer">暂无参考答案</view>
                </block>
              </view>
            </view>
            <view style="height: 60rpx;"></view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </block>

  <!-- 内容结束指示器 -->
  <view class="content-end-indicator">
    <view class="indicator-line"></view>
    <text class="indicator-text">内容结束</text>
    <view class="indicator-line"></view>
  </view>

  <!-- 滑动引导提示 -->
  <view class="swipe-guide-overlay" wx:if="{{showSwipeGuide}}" bindtap="onGuideClick">
    <view class="swipe-guide-content {{guideAnimating ? 'guide-show' : ''}}">
      <view class="guide-icon">
        <view class="swipe-arrow">
          <text class="arrow-icon">↓</text>
        </view>
      </view>
      <view class="guide-text">
        <view class="guide-title">向下滑动</view>
        <view class="guide-subtitle">查看更多题目</view>
      </view>
      <view class="guide-tip">点击任意位置关闭</view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="footer">
    <view class="actions">
      <button class="action-btn {{question.isCollected ? 'collected' : 'uncollected'}}" bindtap="toggleCollect">
        <view class="btn-content">
          <text class="icon-star {{question.isCollected ? 'star-filled' : 'star-outline'}}">{{question.isCollected ? '★' : '☆'}}</text>
          <text class="btn-text">{{question.isCollected ? '已收藏' : '收藏'}}</text>
        </view>
        <view class="btn-ripple"></view>
      </button>
    </view>
  </view>
</view>