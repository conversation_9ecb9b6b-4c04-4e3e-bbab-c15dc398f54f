<view class="container">
  <block wx:if="{{loading}}">
    <!-- 骨架屏 -->
    <view class="skeleton-card">
      <view class="skeleton-title">
        <view class="skeleton-title-inner"></view>
      </view>
      <view class="skeleton-tags">
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
        <view class="skeleton-tag"></view>
      </view>
    </view>
    <view class="skeleton-content">
      <view class="skeleton-section">
        <view class="skeleton-section-title"></view>
        <view class="skeleton-section-content"></view>
      </view>
      <view class="skeleton-section">
        <view class="skeleton-section-title"></view>
        <view class="skeleton-section-content"></view>
      </view>
    </view>
  </block>

  <block wx:else>
    <swiper
      style="height: 100vh;"
      vertical
      current="{{swiperCurrent}}"
      bindchange="onSwiperChange"
      bindanimationfinish="onSwiperAnimationFinish"
      easing-function="easeInOutCubic"
      duration="300"
      indicator-dots="{{!singleMode}}"
      circular="{{!singleMode}}"
      disable-touch="{{singleMode}}"
    >
      <swiper-item wx:for="{{swiperQuestions}}" wx:key="uniqueKey" wx:for-item="item" wx:for-index="idx">
        <scroll-view scroll-y="true" style="height: 100vh;">
          <view class="swiper-question">
            <view class="title-card">
              <view class="title">{{item.title}}</view>
              <view class="tags">
                <view wx:for="{{item.tags}}" wx:key="*this" class="tag">{{item}}</view>
              </view>
            </view>
            <view class="knowledge-card">
              <view class="section-title">相关知识点</view>
              <view class="section-content">
                <view wx:for="{{item.keyPoints}}" wx:key="*this" class="key-point">• {{item}}</view>
              </view>
            </view>
            <view class="answer-card">
              <view class="section-title answer-title">
                参考答案
              </view>
              <view class="section-content">
                <block wx:if="{{item.answerLoading}}">
                  <view class="answer-loading">答案加载中...</view>
                </block>
                <block wx:elif="{{item.answerHtml}}">
                  <rich-text nodes="{{item.answerHtml}}" />
                </block>
                <block wx:else>
                  <view class="empty-answer">暂无参考答案</view>
                </block>
              </view>
            </view>
            <view style="height: 60rpx;"></view>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>
  </block>

  <!-- 底部操作栏 -->
  <view class="footer">
    <view class="actions">
      <button class="action-btn" wx:class="{{question.isCollected ? 'collected' : 'uncollected'}}" bindtap="toggleCollect">
        <text class="icon-star"></text>
        {{question.isCollected ? '已收藏' : '收藏'}}
      </button>
    </view>
  </view>
</view>