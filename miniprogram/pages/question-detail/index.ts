/// <reference types="miniprogram-api-typings" />
import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse } from '../../types/api';
import { request } from '../../utils/request';
import { getQuestionCache, setQuestionCache } from '../../utils/questionCache';



interface Question {
  id: string;
  title: string;
  description: string;
  answer: string;
  keyPoints: string[];
  tags: string[];
  views: number;
  likes: number;
  isLiked: boolean;
  isCollected: boolean;
}

interface IPageData {
  question: Question;
  answerNodes: any[];
  loading: boolean;
  pageEnterTime: number;
  questionIds: string[];
  currentIndex: number;
  swiperQuestions: any[];
  swiperCurrent: number;
  questionCache: Record<string, any>;
  collectStatusCache: Record<string, boolean>;
  answerOpenArr: boolean[];
  singleMode: boolean;
  answerLoading: boolean;
  questionViewStartTime: number | null;
  swiperItemStyles: string[];
}

Page<IPageData, any>({
  data: {
    question: {
      id: '',
      title: '',
      description: '',
      answer: '',
      keyPoints: [],
      tags: [],
      views: 0,
      likes: 0,
      isLiked: false,
      isCollected: false
    },
    answerNodes: [],
    loading: true,
    pageEnterTime: 0,
    questionIds: [],
    currentIndex: 0,
    swiperQuestions: [],
    swiperCurrent: 1,
    questionCache: {},
    collectStatusCache: {},
    answerOpenArr: [false, false, false],
    singleMode: false,
    answerLoading: false,
    questionViewStartTime: null,
    swiperItemStyles: ['', '', ''],
  },

  async onLoad(options: any) {
    console.log('question-detail onLoad options:', options);

    // 解析参数
    const questionId = options.id;
    let questionIds: string[] = [];
    let currentIndex = 0;
    let singleMode = false;

    if (options.questionIds) {
      try {
        questionIds = JSON.parse(decodeURIComponent(options.questionIds));
        console.log('parsed questionIds:', questionIds);
      } catch (e) {
        console.error('Failed to parse questionIds:', e);
        questionIds = [];
      }
    }

    if (options.index) {
      currentIndex = parseInt(options.index, 10) || 0;
      console.log('parsed index from options:', currentIndex);
    } else if (questionIds.length > 0) {
      currentIndex = questionIds.indexOf(questionId);
      console.log('calculated index from questionIds:', currentIndex);
    }

    // 兼容只传id的情况（如收藏/历史页面）
    if (!questionIds.length) {
      questionIds = [questionId];
      currentIndex = 0;
      singleMode = true;
      console.log('single mode activated');
    }

    console.log('final params:', { questionId, questionIds, currentIndex, singleMode });

    this.setData({ questionIds, currentIndex, pageEnterTime: Date.now(), singleMode });
    await this.initSwiperQuestions(currentIndex);
  },

  async initSwiperQuestions(currentIndex: number) {
    console.log('initSwiperQuestions called with currentIndex:', currentIndex);

    const { questionIds, questionCache, collectStatusCache, singleMode } = this.data;
    console.log('initSwiperQuestions data:', { questionIds: questionIds.length, singleMode, currentIndex });

    // 检查是否需要加载更多题目
    if (!singleMode && currentIndex >= questionIds.length - 5) {
      console.log('Approaching end of list, loading more questions...');
      await this.loadMoreQuestionIds();
    }

    // 初始化时重置计时
    this.setData({ questionViewStartTime: Date.now() });
    let swiperQuestions: any[] = [];

    if (singleMode) {
      console.log('single mode: loading single question');
      const id = questionIds[0];
      let q = questionCache[id];
      if (!q) {
        q = await this.loadQuestionData(id);
      }

      // 加载收藏状态
      let isCollected = collectStatusCache[id];
      if (isCollected === undefined) {
        isCollected = await this.loadCollectStatus(id);
      }

      const answerHtml = this.parseAnswerToHtml(q.answer || '');
      const questionWithStatus = { ...q, uniqueKey: id + '-0', answerHtml, isCollected };
      swiperQuestions = [questionWithStatus];

      this.setData({
        swiperQuestions,
        swiperCurrent: 0,
        question: questionWithStatus,
        loading: false,
        answerOpenArr: [false, false, false]
      });
      return;
    }

    console.log('multi mode: loading 3 questions around index', currentIndex);
    const ids = [questionIds[currentIndex - 1], questionIds[currentIndex], questionIds[currentIndex + 1]];
    console.log('target ids:', ids);

    // 预加载题目数据和收藏状态
    const loadPromises = ids.map(async (id, i) => {
      if (!id) {
        return { uniqueKey: 'empty-init-' + i };
      }

      let q = questionCache[id];
      if (!q) {
        q = await this.loadQuestionData(id);
      }

      // 预加载收藏状态
      let isCollected = collectStatusCache[id];
      if (isCollected === undefined) {
        isCollected = await this.loadCollectStatus(id);
      }

      const answerHtml = this.parseAnswerToHtml(q.answer || '');
      return { ...q, uniqueKey: id + '-init-' + i, answerHtml, isCollected };
    });

    const qArr = await Promise.all(loadPromises);
    const currentQ = qArr[1] || {};
    console.log('current question (middle one):', currentQ.title || 'no title', 'isCollected:', currentQ.isCollected);

    this.setData({
      swiperQuestions: qArr,
      swiperCurrent: 1,
      question: currentQ,
      loading: false,
      answerOpenArr: [false, false, false],
    });

    // 预加载相邻题目的收藏状态（后台进行）
    this.preloadAdjacentCollectStatus(currentIndex);
  },

  async loadQuestionData(id: string) {
    // 优先查缓存
    let cache = this.data.questionCache[id] || getQuestionCache(id);
    if (cache) {
      this.data.questionCache[id] = cache;
      console.log('loadQuestionData from cache:', id, cache);
      return cache;
    }
    // 请求接口
    try {
      const res = await request({ url: `${CURRENT_API_BASE_URL}/questions/${id}`, method: 'GET' });
      console.log('loadQuestionData from api:', id, res);
      if (res && res.code === 0 && res.data) {
        const answerNodes = this.parseAnswerToCustomNodes(res.data.answer || '');
        cache = { ...res.data, answerNodes };
        this.data.questionCache[id] = cache;
        setQuestionCache(id, cache);
        return cache;
      }
    } catch (e) { console.error('loadQuestionData error:', e); }
    return {};
  },

  // 新增：上报题目浏览时长的独立方法
  async recordQuestionViewDuration() {
    const { question, questionViewStartTime } = this.data;
    if (question && question.id && questionViewStartTime) {
      const duration = Math.floor((Date.now() - questionViewStartTime) / 1000);
      try {
        await request({
          url: `${CURRENT_API_BASE_URL}/question-views`,
          method: 'POST',
          data: {
            questionId: question.id,
            viewedAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
            duration
          },
        });
      } catch (err) {
        console.error('Failed to record view:', err);
      }
    }
    this.setData({ questionViewStartTime: Date.now() });
  },

  onSwiperChange(e: any) {
    const newCurrent = e.detail.current;
    let { currentIndex, questionIds, singleMode } = this.data;
    if (singleMode) return; // 单题模式无需切换

    console.log('onSwiperChange:', { newCurrent, currentIndex, totalQuestions: questionIds.length });

    // 计算新的题目索引
    let newIndex = currentIndex;
    let direction = '';
    if (newCurrent === 2 && currentIndex < questionIds.length - 1) {
      // 向下滑动到下一题
      newIndex = currentIndex + 1;
      direction = 'next';
    } else if (newCurrent === 0 && currentIndex > 0) {
      // 向上滑动到上一题
      newIndex = currentIndex - 1;
      direction = 'prev';
    } else {
      // 边界情况处理
      if (newCurrent === 0 && currentIndex === 0) {
        console.log('Already at first question, cannot go up');
        // 重置到中间位置
        this.setData({ swiperCurrent: 1 });
        return;
      } else if (newCurrent === 2 && currentIndex >= questionIds.length - 1) {
        console.log('Already at last question, cannot go down');
        // 重置到中间位置
        this.setData({ swiperCurrent: 1 });
        return;
      }
      // 其他无效滑动
      return;
    }

    console.log('Will switch to question index:', newIndex, 'direction:', direction);

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 只更新索引，不立即更新内容
    this.setData({ currentIndex: newIndex });
  },

  onSwiperTransition(e: any) {
    const { singleMode } = this.data;
    if (singleMode) return;

    const { dy } = e.detail;
    // 计算滑动进度 (0-1)，使用固定的屏幕高度值
    const progress = Math.abs(dy) / 800; // 使用固定值避免频繁调用getSystemInfo
    const clampedProgress = Math.min(Math.max(progress, 0), 1);

    console.log('onSwiperTransition:', { dy, progress: clampedProgress });

    // 根据滑动进度计算每个item的样式
    this.updateSwiperItemStyles(clampedProgress, dy > 0 ? 'down' : 'up');
  },

  updateSwiperItemStyles(progress: number, direction: 'up' | 'down') {
    const styles = ['', '', ''];

    // 当前item (中间的，索引1)
    const currentScale = 1 - progress * 0.05; // 从1缩放到0.95
    const currentOpacity = 1 - progress * 0.3; // 从1透明度到0.7
    const currentTranslateY = direction === 'down' ? progress * 20 : -progress * 20; // 轻微位移

    styles[1] = `transform: scale(${currentScale}) translateY(${currentTranslateY}rpx); opacity: ${currentOpacity};`;

    // 目标item (即将显示的)
    const targetIndex = direction === 'down' ? 2 : 0;
    const targetScale = 0.95 + progress * 0.05; // 从0.95缩放到1
    const targetOpacity = 0.6 + progress * 0.4; // 从0.6透明度到1
    const targetTranslateY = direction === 'down' ?
      (1 - progress) * 30 : // 从下方30rpx位移到0
      -(1 - progress) * 30; // 从上方-30rpx位移到0

    styles[targetIndex] = `transform: scale(${targetScale}) translateY(${targetTranslateY}rpx); opacity: ${targetOpacity};`;

    // 另一个item保持原样
    const otherIndex = direction === 'down' ? 0 : 2;
    styles[otherIndex] = `transform: scale(0.95) translateY(${direction === 'down' ? -30 : 30}rpx); opacity: 0.6;`;

    this.setData({ swiperItemStyles: styles });
  },

  onSwiperAnimationFinish() {
    const { currentIndex, singleMode } = this.data;
    if (singleMode) return;

    console.log('onSwiperAnimationFinish:', { currentIndex });

    // 重置所有item样式
    this.setData({
      swiperItemStyles: ['', '', '']
    });

    // 直接更新内容，不显示加载提示
    this.updateSwiperQuestionsAfterAnimation(currentIndex);
  },



  showLoadingState() {
    // 显示优雅的加载提示
    wx.showLoading({
      title: '加载中...',
      mask: false
    });

    // 设置超时自动隐藏
    setTimeout(() => {
      wx.hideLoading();
    }, 3000);
  },

  hideLoadingState() {
    wx.hideLoading();
  },

  async updateSwiperQuestionsAfterAnimation(currentIndex: number) {
    const { questionIds, questionCache, collectStatusCache, question, questionViewStartTime } = this.data;

    // 检查是否需要加载更多题目
    if (currentIndex >= questionIds.length - 5) {
      console.log('Approaching end of list, loading more questions...');
      await this.loadMoreQuestionIds();
    }

    console.log('updateSwiperQuestionsAfterAnimation: loading 3 questions around index', currentIndex);
    const ids = [questionIds[currentIndex - 1], questionIds[currentIndex], questionIds[currentIndex + 1]];
    console.log('target ids:', ids);

    // 预加载所有需要的题目数据和收藏状态
    const loadPromises = ids.map(async (id, i) => {
      if (!id) {
        return { uniqueKey: 'empty-' + currentIndex + '-' + i };
      }

      let q = questionCache[id];
      if (!q) {
        q = await this.loadQuestionData(id);
      }

      // 预加载收藏状态
      let isCollected = collectStatusCache[id];
      if (isCollected === undefined) {
        isCollected = await this.loadCollectStatus(id);
      }

      const answerHtml = this.parseAnswerToHtml(q.answer || '');
      return {
        ...q,
        uniqueKey: id + '-' + currentIndex + '-' + i,
        answerHtml,
        isCollected
      };
    });

    const qArr = await Promise.all(loadPromises);
    const currentQ = qArr[1] || {};
    console.log('current question (middle one):', currentQ.title || 'no title');

    // 一次性更新所有数据
    const updateData: any = {
      swiperQuestions: qArr,
      swiperCurrent: 1,
      question: currentQ,
      answerOpenArr: [false, false, false],
      questionViewStartTime: Date.now()
    };

    this.setData(updateData);

    // 添加轻微的成功反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 异步处理其他操作
    this.recordPreviousQuestionView(question, questionViewStartTime);

    // 预加载相邻题目的收藏状态（后台进行，不阻塞UI）
    this.preloadAdjacentCollectStatus(currentIndex);
  },

  async updateSwiperQuestions(currentIndex: number) {
    const { questionIds, questionCache, question, questionViewStartTime } = this.data;

    console.log('updateSwiperQuestions: loading 3 questions around index', currentIndex);
    const ids = [questionIds[currentIndex - 1], questionIds[currentIndex], questionIds[currentIndex + 1]];
    console.log('target ids:', ids);

    // 预加载所有需要的题目数据，减少异步操作
    const loadPromises = ids.map(async (id, i) => {
      if (!id) {
        return { uniqueKey: 'empty-' + currentIndex + '-' + i };
      }

      let q = questionCache[id];
      if (!q) {
        q = await this.loadQuestionData(id);
      }
      const answerHtml = this.parseAnswerToHtml(q.answer || '');
      return { ...q, uniqueKey: id + '-' + currentIndex + '-' + i, answerHtml };
    });

    const qArr = await Promise.all(loadPromises);
    const currentQ = qArr[1] || {};
    console.log('current question (middle one):', currentQ.title || 'no title');

    // 一次性更新所有数据，减少setData调用
    const updateData: any = {
      swiperQuestions: qArr,
      swiperCurrent: 1,
      question: currentQ,
      answerOpenArr: [false, false, false],
      questionViewStartTime: Date.now()
    };

    this.setData(updateData);

    // 异步上报上一个题目的浏览时长和获取收藏状态，不阻塞UI更新
    this.recordPreviousQuestionView(question, questionViewStartTime);
    if (currentQ && currentQ.id) {
      this.fetchCollectStatus(currentQ.id);
    }
  },

  async recordPreviousQuestionView(question: any, questionViewStartTime: number | null) {
    if (question && question.id && questionViewStartTime) {
      const duration = Math.floor((Date.now() - questionViewStartTime) / 1000);
      try {
        await request({
          url: `${CURRENT_API_BASE_URL}/question-views`,
          method: 'POST',
          data: {
            questionId: question.id,
            viewedAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
            duration
          },
        });
      } catch (err) {
        console.error('Failed to record view:', err);
      }
    }
  },

  async loadCollectStatus(questionId: string): Promise<boolean> {
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/favorites/${questionId}/is-favorite`,
        method: 'GET'
      });

      const isCollected = res && res.code === 0 && res.data;

      // 缓存收藏状态
      this.setData({
        [`collectStatusCache.${questionId}`]: isCollected
      });

      console.log(`Loaded collect status for ${questionId}:`, isCollected);
      return isCollected;
    } catch (err) {
      console.error('Failed to load collect status:', err);
      return false;
    }
  },

  async preloadAdjacentCollectStatus(currentIndex: number) {
    const { questionIds, collectStatusCache } = this.data;

    // 预加载前后各2个题目的收藏状态
    const preloadRange = 2;
    const startIndex = Math.max(0, currentIndex - preloadRange);
    const endIndex = Math.min(questionIds.length - 1, currentIndex + preloadRange);

    const preloadPromises = [];
    for (let i = startIndex; i <= endIndex; i++) {
      const questionId = questionIds[i];
      if (questionId && collectStatusCache[questionId] === undefined) {
        preloadPromises.push(this.loadCollectStatus(questionId));
      }
    }

    if (preloadPromises.length > 0) {
      console.log(`Preloading collect status for ${preloadPromises.length} questions`);
      await Promise.all(preloadPromises);
    }
  },

  async loadMoreQuestionIds() {
    // 分页加载更多题目ID，追加到questionIds
    // 这里假设每页25条，当前已加载questionIds.length
    const page = Math.floor(this.data.questionIds.length / 25) + 1;
    console.log('Loading more questions, page:', page);

    try {
      // 构建请求参数，这里需要从URL参数或其他地方获取筛选条件
      // 由于详情页没有直接的筛选条件，我们使用基础的分页请求
      const requestData: any = {
        page,
        limit: 25
      };

      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions`,
        method: 'GET',
        data: requestData
      });

      if (res && res.code === 0 && Array.isArray(res.data.list)) {
        const newIds = res.data.list.map((q: any) => q.id);
        if (newIds.length > 0) {
          console.log('Loaded more questions:', newIds.length);
          this.setData({ questionIds: [...this.data.questionIds, ...newIds] });
        } else {
          console.log('No more questions to load');
        }
      }
    } catch (e) {
      console.error('Failed to load more questions:', e);
    }
  },

  async onUnload() {
    const { question, questionViewStartTime } = this.data;
    if (!question || !question.id || !questionViewStartTime) {
      console.warn('onUnload: Question data or ID is missing, skipping view record.');
      return;
    }
    const duration = Math.floor((Date.now() - questionViewStartTime) / 1000);
    try {
      await request({
        url: `${CURRENT_API_BASE_URL}/question-views`,
        method: 'POST',
        data: {
          questionId: question.id,
          viewedAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
          duration
        },
      });
    } catch (err) {
      console.error('Failed to record view:', err);
    }
  },

  async fetchQuestionDetail(questionId: string) {
    this.setData({ loading: true });
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions/${questionId}`,
        method: 'GET',
      });
      const { code, data: questionData, msg } = res as ApiResponse<Question>;
      if (code !== 0 || !questionData) {
        wx.showToast({ title: msg || '获取题目详情失败', icon: 'none' });
        throw new Error(msg || '获取题目详情失败');
      }
      let isCollected = false;
      try {
        const favRes = await request({
          url: `${CURRENT_API_BASE_URL}/favorites/${questionId}/is-favorite`,
          method: 'GET',
        });
        const favApi = favRes as ApiResponse<boolean>;
        if (favApi.code === 0) {
          isCollected = favApi.data;
        }
      } catch (favErr) {
        console.error('获取收藏状态请求本身失败:', favErr);
      }
      const finalQuestionData = { ...questionData, isCollected };
      const answerNodes = this.parseAnswerToCustomNodes(finalQuestionData.answer);
      this.setData({
        question: finalQuestionData,
        answerNodes,
        loading: false,
      });
    } catch (error: any) {
      console.error('获取题目详情或收藏状态过程中发生错误:', error.message || error);
      this.setData({ loading: false });
    }
  },

  parseAnswerToCustomNodes(answer: string) {
    const lines = answer.split('\n');
    const nodes: any[] = [];
    lines.forEach(line => {
      const cleanLine = line.replace(/^\*\s*/, '');
      // 1. 数字点分点（如 1. xxx: 或 1. xxx：或 1. xxx. 或 1. xxx。）
      const numPoint = cleanLine.match(/^(\d+\.)\s*(.*?)([：:.。])\s*(.*)$/);
      if (numPoint) {
        const prefixText = '🟦' + numPoint[1] + ' ' + numPoint[2] + numPoint[3];
        const contentText = numPoint[4];
        nodes.push({ type: 'list', prefix: prefixText, content: contentText });
      }
      // 2. 短横线分点（如 - xxx）
      else if (/^-\s+/.test(cleanLine)) {
        const dashPoint = cleanLine.match(/^(-)\s+(.*)$/);
        if (dashPoint) {
          nodes.push({ type: 'list', prefix: '🟦- ', content: dashPoint[2] });
        }
      }
      // 3. 空行
      else if (cleanLine.trim() === '') {
        nodes.push({ type: 'empty' });
      }
      // 4. 普通文本
      else {
        nodes.push({ type: 'text', text: cleanLine });
      }
    });
    return nodes;
  },

  async toggleLike() {
    const { question } = this.data;
    if (!question || !question.id) return;
    const questionId = question.id;
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions/${questionId}/like`,
        method: 'POST',
      });
      const { code, msg, data } = res as ApiResponse<{ likes: number; isLiked: boolean } | null>;
      if (code !== 0) {
        wx.showToast({ title: msg || '操作失败', icon: 'none' });
        return;
      }
      if (data && typeof data.likes === 'number' && typeof data.isLiked === 'boolean') {
        this.setData({
          'question.isLiked': data.isLiked,
          'question.likes': data.likes
        });
      } else {
        const currentQuestion = this.data.question;
        const newIsLiked = !currentQuestion.isLiked;
        const newLikes = currentQuestion.likes + (newIsLiked ? 1 : -1);
        this.setData({
          'question.isLiked': newIsLiked,
          'question.likes': newLikes < 0 ? 0 : newLikes
        });
      }
    } catch (err) {
      console.error('Like request failed:', err);
      wx.showToast({ title: '点赞操作失败', icon: 'none' });
    }
  },

  async toggleCollect() {
    const { question } = this.data;
    if (!question || !question.id) return;

    const questionId = question.id;
    const isCollected = question.isCollected;

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 立即更新UI状态，提供即时反馈
    this.setData({
      'question.isCollected': !isCollected,
    });

    let apiUrl: string;
    let apiMethod: 'POST' | 'DELETE';
    let apiData: any = {};
    if (isCollected) {
      apiMethod = 'DELETE';
      apiUrl = `${CURRENT_API_BASE_URL}/favorites/${questionId}`;
      apiData = {};
    } else {
      apiMethod = 'POST';
      apiUrl = `${CURRENT_API_BASE_URL}/favorites/${questionId}`;
      apiData = {};
    }

    try {
      const res = await request({
        url: apiUrl,
        method: apiMethod,
        data: apiData,
      });
      const { code, msg } = res as ApiResponse;
      if (code !== 0) {
        // 如果请求失败，回滚UI状态
        this.setData({
          'question.isCollected': isCollected,
        });
        wx.showToast({ title: msg || (isCollected ? '取消收藏失败' : '收藏失败'), icon: 'none' });
        return;
      }

      // 更新缓存
      this.setData({
        [`collectStatusCache.${questionId}`]: !isCollected
      });

      // 成功时的反馈
      if (!isCollected) {
        // 收藏成功时的特殊反馈
        wx.vibrateShort({
          type: 'medium'
        });
        wx.showToast({
          title: '收藏成功',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: '取消收藏',
          icon: 'none',
          duration: 1000
        });
      }
    } catch (err) {
      console.error('Collect request failed:', err);
      // 如果请求失败，回滚UI状态
      this.setData({
        'question.isCollected': isCollected,
      });
      wx.showToast({ title: '操作失败', icon: 'none' });
    }
  },

  onShareAppMessage() {
    const { question } = this.data;
    if (!question || !question.id) { // Guard clause
        console.log('分享题目信息不完整');
        return {
            title: '分享题目', // Default title
            path: '/pages/index/index' // Default path
        };
    }
    console.log('分享题目，ID:', question.id);
    return {
      title: question.title,
      path: `/pages/question-detail/index?id=${question.id}`
    };
  },

  async fetchCollectStatus(questionId: string) {
    const { collectStatusCache } = this.data;

    // 先检查缓存
    if (collectStatusCache[questionId] !== undefined) {
      this.setData({
        'question.isCollected': collectStatusCache[questionId]
      });
      return;
    }

    // 缓存中没有，则加载
    const isCollected = await this.loadCollectStatus(questionId);
    this.setData({
      'question.isCollected': isCollected
    });
  },

  onReady() {
    // 页面初次渲染完成后，后台拉取最新数据并更新缓存
    const questionId = this.data.question && this.data.question.id;
    if (questionId) {
      this.fetchAndUpdateQuestionDetail(questionId);
    }
  },

  async fetchAndUpdateQuestionDetail(questionId: string) {
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/questions/${questionId}`,
        method: 'GET',
      });
      if (res && res.code === 0 && res.data) {
        const answerNodes = this.parseAnswerToCustomNodes(res.data.answer || '');
        const cacheData = {
          ...res.data,
          answerNodes
        };
        this.setData({ question: res.data, answerNodes });
        setQuestionCache(questionId, cacheData);
      }
    } catch (e) {
      // 忽略异常
    }
  },

  toggleAnswer(e: any) {
    const idx = e.currentTarget.dataset.idx;
    const arr = this.data.answerOpenArr.slice();
    arr[idx] = !arr[idx];
    this.setData({ answerOpenArr: arr });
  },

  // 新增：将 answer 解析为 html，分点前缀用 <b> 包裹
  parseAnswerToHtml(answer: string): string {
    const lines = answer.split('\n');
    return lines.map(line => {
      const cleanLine = line.replace(/^\*\s*/, '');
      // 数字点分点
      const numPoint = cleanLine.match(/^(\d+\.)\s*(.*?)([：:.。])\s*(.*)$/);
      if (numPoint) {
        const prefix = `<b>${numPoint[1]} ${numPoint[2]}${numPoint[3]}</b>`;
        const content = numPoint[4];
        return `<p>${prefix} ${content}</p>`;
      }
      // 短横线分点
      const dashPoint = cleanLine.match(/^(-)\s+(.*)$/);
      if (dashPoint) {
        return `<p><b>-</b> ${dashPoint[2]}</p>`;
      }
      if (cleanLine.trim() === '') return '<br/>';
      return `<p>${cleanLine}</p>`;
    }).join('');
  },
});