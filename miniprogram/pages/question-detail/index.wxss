.container {
  background: linear-gradient(180deg, #f3f6fb 0%, #e8f2ff 100%);
  box-sizing: border-box;
  padding-bottom: 120rpx;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(64,158,255,0.03) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30rpx, -30rpx) rotate(120deg); }
  66% { transform: translate(-20rpx, 20rpx) rotate(240deg); }
}



/* swiper-item基础样式 */
.swiper-question {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  /* 移除transition，使用动态样式控制 */
}

.title-card {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  box-shadow:
    0 8rpx 32rpx rgba(64, 158, 255, 0.12),
    0 2rpx 8rpx rgba(64, 158, 255, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  margin: 48rpx 32rpx 32rpx 32rpx;
  padding: 48rpx 36rpx 32rpx 36rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  min-height: 120rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.title-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(120deg, rgba(64,158,255,0.08) 0%, rgba(255,255,255,0.12) 100%);
  pointer-events: none;
}

/* 右侧元信息区域 */
.meta-info-area {
  position: absolute;
  top: 0rpx;
  right: 20rpx;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}

/* 公司图标样式 */
.company-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  box-shadow:
    0 4rpx 12rpx rgba(64, 158, 255, 0.15),
    0 1rpx 3rpx rgba(64, 158, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.company-icon-container:hover {
  transform: scale(1.05);
  box-shadow:
    0 6rpx 16rpx rgba(64, 158, 255, 0.2),
    0 2rpx 6rpx rgba(64, 158, 255, 0.15);
}

.company-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
}

.company-name {
  font-size: 20rpx;
  font-weight: 600;
  color: #409EFF;
  text-align: center;
  line-height: 1.2;
  max-width: 60rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 元信息标签组 */
.meta-tags {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
  max-width: 120rpx;
}

/* 元信息标签基础样式 */
.meta-tag {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  animation: metaTagFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* PM类型标签 */
.pm-type-tag {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9) 0%, rgba(100, 179, 255, 0.85) 100%);
  color: #ffffff;
  border-color: rgba(64, 158, 255, 0.3);
}

.pm-type-tag:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 1) 0%, rgba(100, 179, 255, 0.95) 100%);
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
}

/* 未来标签样式预留 */
.future-tag {
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.9) 0%, rgba(255, 193, 60, 0.85) 100%);
  color: #ffffff;
  border-color: rgba(250, 173, 20, 0.3);
}

.future-tag:hover {
  background: linear-gradient(135deg, rgba(250, 173, 20, 1) 0%, rgba(255, 193, 60, 0.95) 100%);
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.3);
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 375px) {
  .meta-info-area {
    top: 0rpx;
    right: 16rpx;
    gap: 10rpx;
  }

  .company-icon-container {
    width: 70rpx;
    height: 70rpx;
  }

  .company-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .company-name {
    font-size: 18rpx;
    max-width: 50rpx;
  }

  .meta-tags {
    max-width: 100rpx;
  }

  .meta-tag {
    font-size: 20rpx;
    padding: 4rpx 12rpx;
  }

  .main-content-area {
    padding-right: 120rpx;
  }

  .title {
    font-size: 36rpx;
  }
}

/* 公司图标加载失败时的样式 */
.company-icon-container .company-icon[src=""] {
  display: none;
}

/* 增强视觉效果 */
.company-icon-container::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 22rpx;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.company-icon-container:hover::before {
  opacity: 1;
}

/* 公司图标入场动画 */
@keyframes companyIconFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-10rpx);
  }
  60% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.company-icon-container {
  animation: companyIconFadeIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 公司图标悬浮效果 */
@keyframes companyIconFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4rpx);
  }
}

.company-icon-container:hover {
  animation: companyIconFloat 2s ease-in-out infinite;
}

/* 图标加载成功时的微动画 */
.company-icon {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.company-icon:hover {
  transform: scale(1.1);
}

/* 特殊公司的主题色彩 */
.company-icon-container[data-company="阿里巴巴"] {
  background: linear-gradient(135deg, rgba(255, 102, 0, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(255, 102, 0, 0.2);
}

.company-icon-container[data-company="腾讯"] {
  background: linear-gradient(135deg, rgba(18, 183, 245, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(18, 183, 245, 0.2);
}

.company-icon-container[data-company="字节跳动"] {
  background: linear-gradient(135deg, rgba(22, 24, 35, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(22, 24, 35, 0.2);
}

.company-icon-container[data-company="美团"] {
  background: linear-gradient(135deg, rgba(255, 184, 0, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(255, 184, 0, 0.2);
}

.company-icon-container[data-company="百度"] {
  background: linear-gradient(135deg, rgba(45, 136, 255, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(45, 136, 255, 0.2);
}

/* 公司名称文字的主题色彩 */
.company-name[data-company="阿里巴巴"] {
  color: #ff6600;
}

.company-name[data-company="腾讯"] {
  color: #12b7f5;
}

.company-name[data-company="字节跳动"] {
  color: #161823;
}

.company-name[data-company="美团"] {
  color: #ffb800;
}

.company-name[data-company="百度"] {
  color: #2d88ff;
}

/* 增加一个微妙的光晕效果 */
.company-icon-container::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(64, 158, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: -2;
}

.company-icon-container:hover::after {
  opacity: 1;
}

/* 元信息标签入场动画 */
@keyframes metaTagFadeIn {
  0% {
    opacity: 0;
    transform: translateX(20rpx) scale(0.9);
  }
  60% {
    opacity: 1;
    transform: translateX(0) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 元信息区域整体入场动画 */
@keyframes metaAreaSlideIn {
  0% {
    opacity: 0;
    transform: translateX(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.meta-info-area {
  animation: metaAreaSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 标签延迟动画 */
.meta-tag:nth-child(1) {
  animation-delay: 0.1s;
}

.meta-tag:nth-child(2) {
  animation-delay: 0.2s;
}

.meta-tag:nth-child(3) {
  animation-delay: 0.3s;
}

/* 元信息区域的整体视觉增强 */
.meta-info-area::before {
  content: '';
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  left: -10rpx;
  background: linear-gradient(135deg,
    rgba(64, 158, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(64, 158, 255, 0.05) 100%);
  border-radius: 24rpx;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.meta-info-area:hover::before {
  opacity: 1;
}

/* PM类型标签的特殊效果 */
.pm-type-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
  border-radius: 16rpx;
  pointer-events: none;
}

/* 标签组合的连接线效果 */
.meta-tags::after {
  content: '';
  position: absolute;
  top: -6rpx;
  right: 50%;
  width: 2rpx;
  height: calc(100% + 12rpx);
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(64, 158, 255, 0.2) 20%,
    rgba(64, 158, 255, 0.2) 80%,
    transparent 100%);
  transform: translateX(50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.meta-info-area:hover .meta-tags::after {
  opacity: 1;
}
/* 主要内容区域 */
.main-content-area {
  position: relative;
  z-index: 1;
  padding-right: 140rpx; /* 为右侧元信息区域留出空间 */
}

.title {
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 18rpx;
  letter-spacing: 1rpx;
  min-height: 50rpx;
  line-height: 1.3;
}

/* 技术标签区域 */
.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0 18rpx;
}

.tech-tag {
  font-size: 24rpx;
  font-weight: 500;
  color: #409EFF;
  background: #eaf4fe;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  margin-bottom: 10rpx;
  box-shadow: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.tech-tag:hover {
  background: #d4edfe;
  transform: translateY(-2rpx);
  box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.15);
}

.content-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
  margin: 0 32rpx 32rpx 32rpx;
  padding: 36rpx 32rpx 32rpx 32rpx;
}
.knowledge-card {
  position: relative;
  background: #ffffff;
  border-radius: 28rpx;
  box-shadow:
    0 4rpx 16rpx rgba(64, 158, 255, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  margin: 0 32rpx 28rpx 32rpx;
  padding: 32rpx 24rpx 20rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.knowledge-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(100deg, rgba(64,158,255,0.06) 0%, rgba(255,255,255,0.10) 100%);
  pointer-events: none;
}
.answer-card {
  position: relative;
  background: #ffffff;
  border-radius: 28rpx;
  box-shadow:
    0 4rpx 16rpx rgba(250, 173, 20, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  margin: 0 32rpx 220rpx 32rpx;
  padding: 32rpx 24rpx 40rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.answer-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(100deg, rgba(250,173,20,0.04) 0%, rgba(255,255,255,0.10) 100%);
  pointer-events: none;
}

.section-title,
.section-content,
.key-point {
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #409EFF;
  padding-left: 16rpx;
  background: none;
}
.section-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
}
.key-point {
  margin-bottom: 16rpx;
}
.key-point:last-child {
  margin-bottom: 0;
}

/* 副标题/说明 */
.subtitle, .desc {
  font-size: 26rpx;
  color: #888;
  font-weight: 400;
}

/* 内容结束指示器 */
.content-end-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 80rpx 32rpx 40rpx 32rpx;
  gap: 24rpx;
}

.indicator-line {
  flex: 1;
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(64, 158, 255, 0.2) 50%, transparent 100%);
}

.indicator-text {
  font-size: 24rpx;
  color: rgba(64, 158, 255, 0.6);
  font-weight: 500;
  letter-spacing: 2rpx;
}

/* 现代化收藏按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg,
    transparent 0%,
    rgba(243, 246, 251, 0.4) 30%,
    rgba(243, 246, 251, 0.8) 70%,
    rgba(243, 246, 251, 1) 100%);
  backdrop-filter: blur(15rpx);
  border: none;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 32rpx 32rpx 56rpx 32rpx;
  height: 140rpx;
}

.actions {
  width: 100%;
  max-width: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-btn {
  position: relative;
  width: 280rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #ffffff;
  box-shadow:
    0 8rpx 32rpx rgba(64, 158, 255, 0.15),
    0 2rpx 8rpx rgba(64, 158, 255, 0.08);
  border: 2rpx solid #e8f2ff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  position: relative;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.icon-star {
  font-size: 40rpx;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.star-outline {
  color: #409EFF;
}

.star-filled {
  color: #faad14;
  animation: starPulse 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.action-btn.uncollected {
  border-color: #e8f2ff;
  background: #ffffff;
}

.action-btn.uncollected .btn-text {
  color: #409EFF;
}

.action-btn.collected {
  border-color: #fff7e6;
  background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
}

.action-btn.collected .btn-text {
  color: #faad14;
}

/* 点击波纹效果 */
.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(64, 158, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  pointer-events: none;
  z-index: 1;
}

.action-btn:active .btn-ripple {
  width: 300rpx;
  height: 300rpx;
  opacity: 0;
}

.action-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 4rpx 16rpx rgba(64, 158, 255, 0.2),
    0 1rpx 4rpx rgba(64, 158, 255, 0.1);
}

.action-btn:active .btn-content {
  transform: scale(0.98);
}

/* 收藏成功动画 */
@keyframes starPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 滑动引导样式 */
.swipe-guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: guideOverlayShow 0.3s ease-out;
}

.swipe-guide-content {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 60rpx 48rpx;
  margin: 0 48rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.8) translateY(40rpx);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.swipe-guide-content.guide-show {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.guide-icon {
  margin-bottom: 32rpx;
}

.swipe-arrow {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: arrowBounce 1.5s ease-in-out infinite;
  box-shadow: 0 8rpx 24rpx rgba(64, 158, 255, 0.3);
}

.arrow-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
}

.guide-text {
  margin-bottom: 32rpx;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.guide-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.guide-tip {
  font-size: 24rpx;
  color: #999;
  padding: 16rpx 24rpx;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 20rpx;
  border: 1rpx solid rgba(64, 158, 255, 0.1);
}

/* 动画效果 */
@keyframes guideOverlayShow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes arrowBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(12rpx);
  }
}

/* 优雅的加载指示器 */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
  padding: 60rpx 0;
}

.loading-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  animation: loadingPulse 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loadingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.skeleton-title {
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.skeleton-title-inner {
  width: 90%;
  height: 36rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx 30rpx;
  border-radius: 0 0 16rpx 16rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 40rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-content {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 120rpx;
}

.skeleton-section {
  width: 100%;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.skeleton-section:first-child {
  margin-bottom: 20rpx;
}

.skeleton-section-title {
  width: 200rpx;
  height: 36rpx;
  margin-bottom: 20rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-section-content {
  width: 100%;
  height: 120rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.section-knowledge {
  margin-bottom: 36rpx;
}

.section-answer {
  border-top: 1rpx solid #eaeaea;
  padding-top: 24rpx;
  background: #f7f8fa;
  border-radius: 0 0 20rpx 20rpx;
}

.answer-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.toggle-btn {
  font-size: 26rpx;
  color: #4F8CFF;
  margin-left: 16rpx;
  background: #e6ecfa;
  border-radius: 24rpx;
  padding: 4rpx 20rpx;
  transition: background 0.2s, color 0.2s;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.06);
  margin-bottom: 36rpx;
  padding: 30rpx;
}

.stats {
  display: flex;
  align-items: center;
}

.stat {
  margin-right: 40rpx;
  text-align: center;
}

.count {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 移除会覆盖.card的样式 */
/* .section-knowledge, .section-answer {
  background: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  border: none;
} */

.answer-h1 {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d5af1;
  margin: 40rpx 0 24rpx 0;
  line-height: 1.4;
  letter-spacing: 1rpx;
}
.answer-h2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #409EFF;
  margin: 32rpx 0 18rpx 0;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}
.text-line {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
  margin-bottom: 24rpx;
  display: block;
  font-weight: 400;
}
.list-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.highlight-span {
  color: #222;
  font-weight: 600;
  display: inline;
}

.list-prefix {
  display: inline-block;
  font-size: 32rpx;
  color: #111;
  background: #eaf4fe;
  border-radius: 10rpx;
  padding: 0 12rpx;
  margin-right: 8rpx;
  font-weight: 700;
}
.list-content {
  display: inline;
}

.answer-loading {
  color: #bbb;
  font-size: 28rpx;
  text-align: center;
  padding: 40rpx 0;
}