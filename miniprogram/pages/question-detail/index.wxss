.container {
  background: linear-gradient(180deg, #f3f6fb 0%, #e8f2ff 100%);
  box-sizing: border-box;
  padding-bottom: 120rpx;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(64,158,255,0.03) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30rpx, -30rpx) rotate(120deg); }
  66% { transform: translate(-20rpx, 20rpx) rotate(240deg); }
}



/* swiper-item基础样式 */
.swiper-question {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
  /* 移除transition，使用动态样式控制 */
}

.title-card {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  box-shadow:
    0 8rpx 32rpx rgba(64, 158, 255, 0.12),
    0 2rpx 8rpx rgba(64, 158, 255, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  margin: 48rpx 32rpx 32rpx 32rpx;
  padding: 48rpx 36rpx 32rpx 36rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  min-height: 120rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.title-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(120deg, rgba(64,158,255,0.08) 0%, rgba(255,255,255,0.12) 100%);
  pointer-events: none;
}
.title {
  position: relative;
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
  margin-bottom: 18rpx;
  z-index: 1;
  letter-spacing: 1rpx;
  min-height: 50rpx;
  line-height: 1.3;
}
.tags {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 0 18rpx;
  z-index: 1;
}
.tag {
  font-size: 24rpx;
  font-weight: 500;
  color: #409EFF;
  background: #eaf4fe;
  padding: 8rpx 24rpx;
  border-radius: 24rpx;
  margin-bottom: 10rpx;
  box-shadow: none;
}

.content-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
  margin: 0 32rpx 32rpx 32rpx;
  padding: 36rpx 32rpx 32rpx 32rpx;
}
.knowledge-card {
  position: relative;
  background: #ffffff;
  border-radius: 28rpx;
  box-shadow:
    0 4rpx 16rpx rgba(64, 158, 255, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  margin: 0 32rpx 28rpx 32rpx;
  padding: 32rpx 24rpx 20rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.knowledge-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(100deg, rgba(64,158,255,0.06) 0%, rgba(255,255,255,0.10) 100%);
  pointer-events: none;
}
.answer-card {
  position: relative;
  background: #ffffff;
  border-radius: 28rpx;
  box-shadow:
    0 4rpx 16rpx rgba(250, 173, 20, 0.08),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
  margin: 0 32rpx 0 32rpx;
  padding: 32rpx 24rpx 20rpx 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.answer-card::before {
  content: '';
  position: absolute;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 0;
  background: linear-gradient(100deg, rgba(250,173,20,0.04) 0%, rgba(255,255,255,0.10) 100%);
  pointer-events: none;
}

.section-title,
.section-content,
.key-point {
  position: relative;
  z-index: 1;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 16rpx;
  border-left: 6rpx solid #409EFF;
  padding-left: 16rpx;
  background: none;
}
.section-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
}
.key-point {
  margin-bottom: 16rpx;
}
.key-point:last-child {
  margin-bottom: 0;
}

/* 副标题/说明 */
.subtitle, .desc {
  font-size: 26rpx;
  color: #888;
  font-weight: 400;
}

/* 现代化收藏按钮 */
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(243, 246, 251, 0.8) 40%, rgba(243, 246, 251, 0.95) 100%);
  backdrop-filter: blur(10rpx);
  border: none;
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 32rpx 32rpx 56rpx 32rpx;
}

.actions {
  width: 100%;
  max-width: 400rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-btn {
  position: relative;
  width: 280rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #ffffff;
  box-shadow:
    0 8rpx 32rpx rgba(64, 158, 255, 0.15),
    0 2rpx 8rpx rgba(64, 158, 255, 0.08);
  border: 2rpx solid #e8f2ff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  position: relative;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.icon-star {
  font-size: 40rpx;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.star-outline {
  color: #409EFF;
}

.star-filled {
  color: #faad14;
  animation: starPulse 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.action-btn.uncollected {
  border-color: #e8f2ff;
  background: #ffffff;
}

.action-btn.uncollected .btn-text {
  color: #409EFF;
}

.action-btn.collected {
  border-color: #fff7e6;
  background: linear-gradient(135deg, #ffffff 0%, #fffbf0 100%);
}

.action-btn.collected .btn-text {
  color: #faad14;
}

/* 点击波纹效果 */
.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(64, 158, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: all 0.6s cubic-bezier(0.4, 0.0, 0.2, 1);
  pointer-events: none;
  z-index: 1;
}

.action-btn:active .btn-ripple {
  width: 300rpx;
  height: 300rpx;
  opacity: 0;
}

.action-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 4rpx 16rpx rgba(64, 158, 255, 0.2),
    0 1rpx 4rpx rgba(64, 158, 255, 0.1);
}

.action-btn:active .btn-content {
  transform: scale(0.98);
}

/* 收藏成功动画 */
@keyframes starPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 骨架屏样式 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}

.skeleton-title {
  padding: 30rpx;
  border-radius: 16rpx 16rpx 0 0;
}

.skeleton-title-inner {
  width: 90%;
  height: 36rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-tags {
  display: flex;
  flex-wrap: wrap;
  padding: 0 30rpx 30rpx;
  border-radius: 0 0 16rpx 16rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 40rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-content {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 120rpx;
}

.skeleton-section {
  width: 100%;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.skeleton-section:first-child {
  margin-bottom: 20rpx;
}

.skeleton-section-title {
  width: 200rpx;
  height: 36rpx;
  margin-bottom: 20rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-section-content {
  width: 100%;
  height: 120rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.section-knowledge {
  margin-bottom: 36rpx;
}

.section-answer {
  border-top: 1rpx solid #eaeaea;
  padding-top: 24rpx;
  background: #f7f8fa;
  border-radius: 0 0 20rpx 20rpx;
}

.answer-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.toggle-btn {
  font-size: 26rpx;
  color: #4F8CFF;
  margin-left: 16rpx;
  background: #e6ecfa;
  border-radius: 24rpx;
  padding: 4rpx 20rpx;
  transition: background 0.2s, color 0.2s;
}

.card {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.06);
  margin-bottom: 36rpx;
  padding: 30rpx;
}

.stats {
  display: flex;
  align-items: center;
}

.stat {
  margin-right: 40rpx;
  text-align: center;
}

.count {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 移除会覆盖.card的样式 */
/* .section-knowledge, .section-answer {
  background: none;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  border: none;
} */

.answer-h1 {
  font-size: 32rpx;
  font-weight: 700;
  color: #2d5af1;
  margin: 40rpx 0 24rpx 0;
  line-height: 1.4;
  letter-spacing: 1rpx;
}
.answer-h2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #409EFF;
  margin: 32rpx 0 18rpx 0;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}
.text-line {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
  margin-bottom: 24rpx;
  display: block;
  font-weight: 400;
}
.list-item {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
  margin-bottom: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
}

.highlight-span {
  color: #222;
  font-weight: 600;
  display: inline;
}

.list-prefix {
  display: inline-block;
  font-size: 32rpx;
  color: #111;
  background: #eaf4fe;
  border-radius: 10rpx;
  padding: 0 12rpx;
  margin-right: 8rpx;
  font-weight: 700;
}
.list-content {
  display: inline;
}

.answer-loading {
  color: #bbb;
  font-size: 28rpx;
  text-align: center;
  padding: 40rpx 0;
}