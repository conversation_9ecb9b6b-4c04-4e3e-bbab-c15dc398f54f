interface IPageData {
  cacheInfo: {
    questionCount: number;
    categoryCount: number;
    settingsCount: number;
  };
  showConfirm: boolean;
  clearing: boolean;
}

interface IPageInstance {
  data: IPageData;
  onLoad: () => void;
  calculateCacheInfo: () => void;
  showClearConfirm: () => void;
  hideConfirm: () => void;
  clearCache: () => void;
  goToContact: () => void;
  stopPropagation: () => void;
}

Page<IPageData, IPageInstance>({
  data: {
    cacheInfo: {
      questionCount: 0,
      categoryCount: 0,
      settingsCount: 0
    },
    showConfirm: false,
    clearing: false
  },

  onLoad() {
    // 页面加载时的初始化
    wx.setNavigationBarTitle({
      title: '清除缓存'
    });
    
    // 计算缓存信息
    this.calculateCacheInfo();
  },

  calculateCacheInfo() {
    try {
      // 计算题目缓存数量
      const questionCacheKeys = wx.getStorageInfoSync().keys.filter(key => 
        key.startsWith('question_cache_') || key === 'question_list_cache'
      );
      
      // 计算分类缓存数量
      const categoryCacheKeys = wx.getStorageInfoSync().keys.filter(key => 
        key === 'pm_types_cache' || key === 'companies_cache'
      );
      
      // 计算设置缓存数量
      const settingsCacheKeys = wx.getStorageInfoSync().keys.filter(key => 
        key.includes('guide_seen') || key.includes('settings') || key === 'token'
      );

      this.setData({
        cacheInfo: {
          questionCount: questionCacheKeys.length,
          categoryCount: categoryCacheKeys.length,
          settingsCount: settingsCacheKeys.length
        }
      });
    } catch (err) {
      console.error('计算缓存信息失败:', err);
      this.setData({
        cacheInfo: {
          questionCount: 0,
          categoryCount: 0,
          settingsCount: 0
        }
      });
    }
  },

  showClearConfirm() {
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
    
    this.setData({ showConfirm: true });
  },

  hideConfirm() {
    this.setData({ showConfirm: false });
  },

  async clearCache() {
    this.setData({ 
      clearing: true,
      showConfirm: false 
    });

    try {
      // 获取所有存储的key
      const storageInfo = wx.getStorageInfoSync();
      const allKeys = storageInfo.keys;
      
      // 需要保留的关键数据（不清除）
      const keepKeys = ['token']; // 保留登录token
      
      // 清除除了保留key之外的所有缓存
      const keysToRemove = allKeys.filter(key => !keepKeys.includes(key));
      
      // 逐个清除缓存
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key);
          console.log('已清除缓存:', key);
        } catch (err) {
          console.error('清除缓存失败:', key, err);
        }
      });

      // 添加成功反馈
      wx.vibrateShort({
        type: 'medium'
      });

      wx.showToast({
        title: '缓存清除成功',
        icon: 'success',
        duration: 2000
      });

      // 重新计算缓存信息
      setTimeout(() => {
        this.calculateCacheInfo();
        this.setData({ clearing: false });
      }, 1000);

    } catch (err) {
      console.error('清除缓存失败:', err);
      this.setData({ clearing: false });
      
      wx.showToast({
        title: '清除缓存失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  goToContact() {
    wx.navigateTo({
      url: '/pages/contact/index'
    });
  },

  stopPropagation() {
    // 阻止事件冒泡
  }
});
