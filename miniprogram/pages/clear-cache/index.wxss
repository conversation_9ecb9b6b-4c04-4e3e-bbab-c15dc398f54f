/* clear-cache.wxss */
.container {
  background: linear-gradient(180deg, #f3f6fb 0%, #e8f2ff 100%);
  min-height: 100vh;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

/* 警告卡片 */
.warning-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx 36rpx;
  margin-bottom: 32rpx;
  box-shadow: 
    0 8rpx 32rpx rgba(255, 193, 7, 0.12),
    0 2rpx 8rpx rgba(255, 193, 7, 0.08);
  border: 2rpx solid #fff3cd;
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
}

.warning-icon {
  flex-shrink: 0;
}

.icon {
  width: 80rpx;
  height: 80rpx;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #856404;
  margin-bottom: 12rpx;
}

.warning-desc {
  font-size: 28rpx;
  color: #856404;
  line-height: 1.5;
}

/* 建议卡片 */
.suggestion-card {
  background: #ffffff;
  border-radius: 28rpx;
  padding: 36rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.suggestion-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(64, 158, 255, 0.1);
}

.suggestion-icon {
  font-size: 32rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #333;
}

/* 缓存信息 */
.cache-info {
  background: #ffffff;
  border-radius: 28rpx;
  padding: 36rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 操作区域 */
.action-section {
  margin-bottom: 32rpx;
}

.clear-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
  border-radius: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  margin-bottom: 16rpx;
}

.clear-btn:disabled {
  background: #ccc;
  box-shadow: none;
}

.clear-btn:active:not(:disabled) {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.action-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 联系客服 */
.contact-section {
  margin-bottom: 40rpx;
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  border: 2rpx solid #409EFF;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.contact-btn:active {
  transform: scale(0.98);
  background: rgba(64, 158, 255, 0.05);
}

.contact-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #409EFF;
}

/* 确认弹窗 */
.confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: overlayShow 0.3s ease-out;
}

.confirm-modal {
  background: #ffffff;
  border-radius: 32rpx;
  margin: 0 48rpx;
  max-width: 600rpx;
  width: 100%;
  overflow: hidden;
  animation: modalShow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.confirm-header {
  padding: 48rpx 36rpx 32rpx 36rpx;
  text-align: center;
}

.confirm-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.confirm-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.confirm-actions {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 96rpx;
  border: none;
  background: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.cancel-btn:active {
  background: #f8f8f8;
}

.confirm-btn {
  color: #ff6b6b;
}

.confirm-btn:active {
  background: rgba(255, 107, 107, 0.1);
}

/* 动画 */
@keyframes overlayShow {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes modalShow {
  0% {
    transform: scale(0.8) translateY(40rpx);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}
