<!--clear-cache.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="title">清除缓存</view>
    <view class="subtitle">谨慎操作，仅在必要时使用</view>
  </view>

  <!-- 警告卡片 -->
  <view class="warning-card">
    <view class="warning-icon">
      <image src="/images/my/clear.svg" class="icon" />
    </view>
    
    <view class="warning-content">
      <view class="warning-title">注意事项</view>
      <view class="warning-desc">大多数情况下您不需要清除缓存功能，除非使用时遇到异常问题。</view>
    </view>
  </view>

  <!-- 建议卡片 -->
  <view class="suggestion-card">
    <view class="suggestion-title">建议优先尝试</view>
    <view class="suggestion-list">
      <view class="suggestion-item">
        <text class="suggestion-icon">💬</text>
        <text class="suggestion-text">联系客服确认问题</text>
      </view>
      <view class="suggestion-item">
        <text class="suggestion-icon">🔄</text>
        <text class="suggestion-text">重启小程序</text>
      </view>
      <view class="suggestion-item">
        <text class="suggestion-icon">📱</text>
        <text class="suggestion-text">检查网络连接</text>
      </view>
    </view>
  </view>

  <!-- 缓存信息 -->
  <view class="cache-info">
    <view class="info-title">缓存信息</view>
    <view class="info-list">
      <view class="info-item">
        <text class="info-label">题目缓存</text>
        <text class="info-value">{{cacheInfo.questionCount}}题</text>
      </view>
      <view class="info-item">
        <text class="info-label">分类缓存</text>
        <text class="info-value">{{cacheInfo.categoryCount}}项</text>
      </view>
      <view class="info-item">
        <text class="info-label">用户设置</text>
        <text class="info-value">{{cacheInfo.settingsCount}}项</text>
      </view>
    </view>
  </view>

  <!-- 清除按钮 -->
  <view class="action-section">
    <button class="clear-btn" bindtap="showClearConfirm" disabled="{{clearing}}">
      <text class="btn-text">{{clearing ? '清除中...' : '清除所有缓存'}}</text>
    </button>
    
    <view class="action-tip">
      清除后需要重新加载数据，可能会影响使用体验
    </view>
  </view>

  <!-- 联系客服按钮 -->
  <view class="contact-section">
    <button class="contact-btn" bindtap="goToContact">
      <text class="contact-text">联系客服</text>
    </button>
  </view>
</view>

<!-- 确认弹窗 -->
<view class="confirm-overlay" wx:if="{{showConfirm}}" bindtap="hideConfirm">
  <view class="confirm-modal" catchtap="stopPropagation">
    <view class="confirm-header">
      <view class="confirm-title">确认清除缓存？</view>
      <view class="confirm-desc">此操作将清除所有本地缓存数据，包括题目、分类和设置信息。清除后需要重新加载数据。</view>
    </view>
    
    <view class="confirm-actions">
      <button class="cancel-btn" bindtap="hideConfirm">取消</button>
      <button class="confirm-btn" bindtap="clearCache">确认清除</button>
    </view>
  </view>
</view>
