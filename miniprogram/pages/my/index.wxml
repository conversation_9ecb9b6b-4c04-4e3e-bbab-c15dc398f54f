<!--index.wxml-->
<view class="container">

  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="avatar" src="/images/face.svg" />
      <view class="user-details">
        <text class="username">{{'产品练习生'}}</text>
        <text class="position">{{userInfo.position || '产品经理'}} · {{'0年经验'}}</text>
      </view>
    </view>


  </view>

  <!-- 功能列表 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-left">
        <image class="menu-icon" src="/images/star.svg" />
        <text class="menu-text">我的收藏</text>
      </view>
      <image class="arrow" src="/images/my/arrow-right.svg" />
    </view>

    <view class="menu-item" bindtap="onStatsTap">
      <view class="menu-left">
        <image class="menu-icon" src="/images/my/history.svg" />
        <text class="menu-text">浏览历史</text>
      </view>
      <image class="arrow" src="/images/my/arrow-right.svg" />
    </view>

    <view class="menu-item" bindtap="goToContact">
      <view class="menu-left">
        <image class="menu-icon" src="/images/my/contact.svg" />
        <text class="menu-text">联系我们</text>
      </view>
      <image class="arrow" src="/images/my/arrow-right.svg" />
    </view>

    <view class="menu-item" bindtap="goToClearCache">
      <view class="menu-left">
        <image class="menu-icon" src="/images/my/clear.svg" />
        <text class="menu-text">清除缓存</text>
      </view>
      <image class="arrow" src="/images/my/arrow-right.svg" />
    </view>

  </view>

</view>