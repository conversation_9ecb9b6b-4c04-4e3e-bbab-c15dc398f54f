<!--index.wxml-->
<view class="container">

  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="avatar" src="/images/face.svg" />
      <view class="user-details">
        <text class="username">{{'产品练习生'}}</text>
        <text class="position">{{userInfo.position || '产品经理'}} · {{'0年经验'}}</text>
      </view>
    </view>


  </view>

  <!-- 功能列表 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="goToFavorites">
      <view class="menu-left">
        <image class="menu-icon" src="/images/star.svg" />
        <text class="menu-text">我的收藏</text>
      </view>
      <image class="arrow" src="/images/arrow-right.png" />
    </view>

    <view class="menu-item" bindtap="onStatsTap">
      <view class="menu-left">
        <image class="menu-icon" src="/images/stats.svg" />
        <text class="menu-text">学习统计</text>
      </view>
      <image class="arrow" src="/images/arrow-right.png" />
    </view>

    <view class="menu-item" bindtap="onSettingsTap">
      <view class="menu-left">
        <image class="menu-icon" src="/images/setting.svg" />
        <text class="menu-text">设置</text>
      </view>
      <image class="arrow" src="/images/arrow-right.png" />
    </view>

    <view class="menu-item" bindtap="onHelpTap">
      <view class="menu-left">
        <image class="menu-icon" src="/images/feedback.svg" />
        <text class="menu-text">帮助与反馈</text>
      </view>
      <image class="arrow" src="/images/arrow-right.png" />
    </view>

    <view class="menu-item" bindtap="onShareTap">
      <view class="menu-left">
        <image class="menu-icon" src="/images/share.svg" />
        <text class="menu-text">分享给朋友</text>
      </view>
      <image class="arrow" src="/images/arrow-right.png" />
    </view>
  </view>

</view> 