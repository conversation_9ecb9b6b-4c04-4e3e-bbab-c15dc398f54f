interface UserInfo {
  name: string;
  position: string;
  experience: string;
  avatarUrl: string;
}

interface IPageData {
  userInfo: UserInfo;
  question?: string;
  answerNodes?: any[];
}

interface IPageInstance {
  data: IPageData;
  onLoad: () => void;
  onStatsTap: () => void;
  onSettingsTap: () => void;
  onHelpTap: () => void;
  onShareTap: () => void;
  onLogout: () => void;
  onShareAppMessage: () => WechatMiniprogram.Page.ICustomShareContent;
  goToFavorites: () => void;
  goToContact: () => void;
  goToClearCache: () => void;
}

Page<IPageData, IPageInstance>({
  data: {
    userInfo: {
      name: '张小明',
      position: '产品经理',
      experience: '3年经验',
      avatarUrl: '/images/default-avatar.png'
    },
    question: '',
    answerNodes: []
  },

  onLoad() {
    // 可以在这里获取用户信息
  },

  onStatsTap() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      // 可选：如果希望未登录时跳转到登录页，取消下面这行的注释
      // wx.navigateTo({ url: '/pages/login/index' });
      return;
    }
    wx.navigateTo({
      url: '/pages/history/index'
    });
  },

  onSettingsTap() {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  onHelpTap() {
    wx.navigateTo({
      url: '/pages/help/index'
    });
  },

  onShareTap() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 执行退出登录逻辑
          wx.clearStorage();
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  onShareAppMessage() {
    return {
      title: '荔枝刷题-学产品，上荔枝',
      path: '/pages/index/index'
    };
  },

  goToFavorites() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/favorites/index'
    });
  },

  goToContact() {
    wx.navigateTo({
      url: '/pages/contact/index'
    });
  },

  goToClearCache() {
    wx.navigateTo({
      url: '/pages/clear-cache/index'
    });
  }
});