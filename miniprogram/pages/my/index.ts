import { CURRENT_API_BASE_URL } from '../../config';
const { request } = require('../../utils/request');

interface UserInfo {
  name: string;
  position: string;
  experience: string;
  avatarUrl: string;
}

interface IPageData {
  userInfo: UserInfo;
  question?: string;
  answerNodes?: any[];
}

interface IPageInstance {
  data: IPageData;
  onLoad: () => void;
  onStatsTap: () => void;
  onSettingsTap: () => void;
  onHelpTap: () => void;
  onShareTap: () => void;
  onLogout: () => void;
  onShareAppMessage: () => WechatMiniprogram.Page.ICustomShareContent;
  goToFavorites: () => void;
  goToFeedback: () => void;
  goToContact: () => void;
  goToClearCache: () => void;
  preloadFavoritesData: () => void;
  preloadHistoryData: () => void;
}

Page<IPageData, IPageInstance>({
  data: {
    userInfo: {
      name: '张小明',
      position: '产品经理',
      experience: '3年经验',
      avatarUrl: '/images/default-avatar.png'
    },
    question: '',
    answerNodes: []
  },

  onLoad() {
    // 可以在这里获取用户信息
  },

  onStatsTap() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      // 可选：如果希望未登录时跳转到登录页，取消下面这行的注释
      // wx.navigateTo({ url: '/pages/login/index' });
      return;
    }

    // 立即跳转页面
    wx.navigateTo({
      url: '/pages/history/index'
    });

    // 同时发起预请求
    this.preloadHistoryData();
  },

  onSettingsTap() {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  onHelpTap() {
    wx.navigateTo({
      url: '/pages/help/index'
    });
  },

  onShareTap() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 执行退出登录逻辑
          wx.clearStorage();
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  onShareAppMessage() {
    return {
      title: '荔枝刷题-学产品，上荔枝',
      path: '/pages/index/index'
    };
  },

  goToFavorites() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 立即跳转页面
    wx.navigateTo({
      url: '/pages/favorites/index'
    });

    // 同时发起预请求
    this.preloadFavoritesData();
  },

  goToFeedback() {
    wx.navigateTo({
      url: '/pages/feedback/index'
    });
  },

  goToContact() {
    wx.navigateTo({
      url: '/pages/contact/index'
    });
  },

  goToClearCache() {
    wx.navigateTo({
      url: '/pages/clear-cache/index'
    });
  },

  // 预加载收藏数据
  async preloadFavoritesData() {
    try {
      console.log('开始预加载收藏数据...');
      const token = wx.getStorageSync('token');
      if (!token) return;

      // 发起收藏列表请求
      const response = await request({
        url: `${CURRENT_API_BASE_URL}/favorites`,
        method: 'GET',
      });

      if (response && response.code === 0) {
        // 将数据缓存到全局，供收藏页面使用
        const app = getApp();
        app.globalData = app.globalData || {};
        app.globalData.preloadedFavorites = {
          data: response.data,
          timestamp: Date.now()
        };
        console.log('收藏数据预加载完成:', response.data.length, '条');
      }
    } catch (error) {
      console.error('预加载收藏数据失败:', error);
    }
  },

  // 预加载历史数据
  async preloadHistoryData() {
    try {
      console.log('开始预加载历史数据...');
      const token = wx.getStorageSync('token');
      if (!token) return;

      // 发起历史记录请求
      const response = await request({
        url: `${CURRENT_API_BASE_URL}/question-views`,
        method: 'GET',
        data: {
          page: 1,
          limit: 20
        }
      });

      if (response && response.code === 0) {
        // 将数据缓存到全局，供历史页面使用
        const app = getApp();
        app.globalData = app.globalData || {};
        app.globalData.preloadedHistory = {
          data: response.data,
          timestamp: Date.now()
        };
        console.log('历史数据预加载完成:', response.data.list?.length || 0, '条');
      }
    } catch (error) {
      console.error('预加载历史数据失败:', error);
    }
  }
});