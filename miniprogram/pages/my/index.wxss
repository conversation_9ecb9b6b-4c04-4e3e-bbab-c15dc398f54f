.container {
  background-color: #f5f5f5;
  padding: 0;
  max-width: 750rpx;
  margin: 0 auto;
}

.header {
  padding: 20rpx 30rpx;
  background: #fff;
}

.header .title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.user-card {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: calc(100% - 60rpx);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.position {
  font-size: 28rpx;
  color: #666;
}

.stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  text-align: center;
}

.stat-num {
  display: block;
  font-size: 40rpx;
  color: #4080FF;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.menu-list {
  margin: 20rpx 30rpx;
  background: #fff;
  border-radius: 12rpx;
  width: calc(100% - 60rpx);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 30rpx;
  color: #333;
}

.arrow {
  width: 32rpx;
  height: 32rpx;
}

.logout-btn {
  margin: 60rpx 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #fff;
  border-radius: 12rpx;
  color: #FF3B30;
  font-size: 32rpx;
  border: 2rpx solid #FF3B30;
} 