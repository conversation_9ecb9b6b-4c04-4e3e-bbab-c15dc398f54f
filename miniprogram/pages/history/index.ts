import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse } from '../../types/api';
import { request } from '../../utils/request';

// 更新后的 HistoryItem 接口
interface ApiHistoryRecord {
  id: string; 
  title: string; 
  createdAt: string; 
  category?: string[]; 
  questionId: string;
  companyName?: string;
  pmTypeName?: string;
}

interface HistoryItem {
  questionId: string;
  questionTitle: string;
  viewedAt: string;
  tags?: string[];
  companyName?: string;
  pmTypeName?: string;
}

interface PaginatedHistoryResponse {
  total: number;
  page: number;
  limit: number;
  records: ApiHistoryRecord[];
}

interface IPageData {
  historyItems: HistoryItem[];
  loading: boolean;
  isLoadingMore: boolean; 
  isEmpty: boolean;
  currentPage: number;
  limit: number;
  hasMore: boolean;
}

Page<IPageData, any>({
  data: {
    historyItems: [],
    loading: true, 
    isLoadingMore: false, 
    isEmpty: false,
    currentPage: 1,
    limit: 50, 
    hasMore: true,
  },

  onLoad() {
    this.fetchHistory(true); 
  },

  async fetchHistory(isRefresh: boolean = false) {
    if (isRefresh) {
      this.setData({ currentPage: 1, historyItems: [], hasMore: true, isEmpty: false });
    }
    if (!this.data.hasMore || this.data.isLoadingMore) {
      if(!isRefresh) return;
    }
    this.setData({
      loading: isRefresh && this.data.historyItems.length === 0,
      isLoadingMore: !isRefresh
    });
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/question-views/user/recent`,
        method: 'GET',
        data: {
          page: this.data.currentPage,
          limit: this.data.limit,
        },
      });
      const apiResponse = res as ApiResponse<PaginatedHistoryResponse>;
      if (apiResponse.code === 0 && apiResponse.data && apiResponse.data.records) {
        const newItems = apiResponse.data.records.map(record => ({
          id: record.id,
          questionId: record.questionId,
          questionTitle: record.title,
          viewedAt: record.viewedAt,
          tags: record.category || [],
          companyName: record.companyName || '',
          pmTypeName: record.pmTypeName || ''
        }));
        const updatedHistoryItems = isRefresh ? newItems : [...this.data.historyItems, ...newItems];
        this.setData({
          historyItems: updatedHistoryItems,
          currentPage: this.data.currentPage + 1,
          hasMore: updatedHistoryItems.length < apiResponse.data.total,
          isEmpty: updatedHistoryItems.length === 0,
        });
      } else {
        wx.showToast({ title: apiResponse.msg || '获取历史记录失败', icon: 'none' });
        if (isRefresh) this.setData({ isEmpty: true });
        this.setData({ hasMore: false });
      }
    } catch (err) {
      console.error('获取浏览历史失败:', err);
      wx.showToast({ title: '网络错误，请重试', icon: 'none' });
      if (isRefresh) this.setData({ isEmpty: true });
      this.setData({ hasMore: false });
    } finally {
      this.setData({ loading: false, isLoadingMore: false });
      if (isRefresh) wx.stopPullDownRefresh();
    }
  },

  formatDate(dateString: string): string {
    if (!dateString) return '';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },
  
  onPullDownRefresh() {
    this.fetchHistory(true); 
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.fetchHistory(); 
    }
  },
}); 