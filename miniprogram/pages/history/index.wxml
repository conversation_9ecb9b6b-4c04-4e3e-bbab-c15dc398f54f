<wxs module="utils" src="./utils.wxs"></wxs>

<view class="container history-page-container">
  <block wx:if="{{loading}}">
    <view class="loading-state">
      <text>加载中...</text>
      <!-- TODO: Consider adding a skeleton component -->
    </view>
  </block>
  <block wx:elif="{{isEmpty}}">
    <view class="empty-state">
      <text class="empty-text">暂无浏览历史记录</text>
    </view>
  </block>
  <block wx:else>
    <view class="history-list">
      <view class="history-entry" wx:for="{{historyItems}}" wx:key="id">
        <question-item question="{{ { id: item.questionId, title: item.questionTitle, companyName: item.companyName, pmTypeName: item.pmTypeName } }}" />
        <view class="viewed-at-time">浏览于: {{utils.formatDate(item.viewedAt)}}</view>
      </view>
    </view>
  </block>
</view> 