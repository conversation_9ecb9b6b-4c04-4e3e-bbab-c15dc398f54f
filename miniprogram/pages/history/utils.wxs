var formatDate = function(dateString) {
  if (!dateString) return '';

  var parts = dateString.split('T');
  var datePart = parts[0];
  var timePart = parts.length > 1 ? parts[1].split('.')[0] : '00:00:00';

  datePart = datePart.replace('-', '/').replace('-', '/'); 

  var compatibleDateString = datePart + ' ' + timePart;
  var date = getDate(compatibleDateString);

  if (isNaN(date.getTime())) {
      // For debugging in WXS, you can't use console.log directly in a way that shows in devtools console easily.
      // One simple way for debugging is to return the problematic string or a specific error message.
      // return "Error: Invalid Date: " + dateString;
      return dateString; // Fallback to original if parsing fails
  }
  
  var year = date.getFullYear();
  var month = (date.getMonth() + 1);
  var day = date.getDate();
  var hours = date.getHours();
  var minutes = date.getMinutes();

  var pad = function(n) {
    return n < 10 ? '0' + n : n;
  }

  return year + '-' + pad(month) + '-' + pad(day) + ' ' + pad(hours) + ':' + pad(minutes);
}

module.exports = {
  formatDate: formatDate
}; 