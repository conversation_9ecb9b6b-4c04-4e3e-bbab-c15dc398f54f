.history-page-container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

.page-header {
  padding: 30rpx;
  background-color: #fff;
  text-align: center;
  border-bottom: 1rpx solid #e9e9e9;
  margin-bottom: 20rpx;
}

.page-title-text {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
  color: #999;
}

.empty-icon {
  width: 150rpx;
  height: 150rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

.history-list {
  padding: 10rpx 20rpx;
}

.history-entry {
  background-color: #ffffff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.viewed-at-time {
  font-size: 24rpx;
  color: #999;
  padding: 10rpx 20rpx;
  text-align: right;
  border-top: 1rpx solid #f0f0f0;
} 