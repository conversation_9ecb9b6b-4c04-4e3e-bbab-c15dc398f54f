import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse } from '../../types/api';
import { request } from '../../utils/request';

interface Favorite {
  id: string;
  userId: number;
  questionId: string;
  createdAt: string;
  updatedAt: string;
  title: string;
  companyName: string;
  pmTypeName: string;
}

Page({
  data: {
    favorites: [] as Favorite[],
    loading: false
  },

  onLoad() {
    this.loadFavorites();
  },

  onShow() {
    this.loadFavorites();
  },

  onPullDownRefresh() {
    this.loadFavorites().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  async loadFavorites() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/favorites`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<Favorite[]>;
      
      if (code !== 0) {
        throw new Error(msg || '获取收藏列表失败');
      }

      this.setData({
        favorites: data
      });
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },
}); 