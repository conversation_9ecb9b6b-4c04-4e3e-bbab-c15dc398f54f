import { CURRENT_API_BASE_URL } from '../../config';
import { ApiResponse } from '../../types/api';
import { request } from '../../utils/request';

interface Favorite {
  id: string;
  userId: number;
  questionId: string;
  createdAt: string;
  updatedAt: string;
  title: string;
  companyName: string;
  pmTypeName: string;
}

Page({
  data: {
    favorites: [] as Favorite[],
    loading: false
  },

  onLoad() {
    // 优先使用预加载数据
    this.loadFavoritesWithPreload();
  },

  onShow() {
    this.loadFavorites();
  },

  onPullDownRefresh() {
    this.loadFavorites().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  async loadFavorites() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const res = await request({
        url: `${CURRENT_API_BASE_URL}/favorites`,
        method: 'GET',
      });
      const { code, data, msg } = res as ApiResponse<Favorite[]>;

      if (code !== 0) {
        throw new Error(msg || '获取收藏列表失败');
      }

      this.setData({
        favorites: data
      });
    } catch (error) {
      console.error('获取收藏列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 优先使用预加载数据的加载方法
  loadFavoritesWithPreload() {
    const app = getApp();
    const preloadedData = app.globalData?.preloadedFavorites;

    // 检查是否有预加载数据且数据新鲜（5分钟内）
    if (preloadedData && (Date.now() - preloadedData.timestamp < 5 * 60 * 1000)) {
      console.log('使用预加载的收藏数据');
      this.setData({
        favorites: preloadedData.data,
        loading: false
      });

      // 清除预加载数据，避免重复使用
      delete app.globalData.preloadedFavorites;
      return;
    }

    // 没有预加载数据或数据过期，正常加载
    console.log('预加载数据不可用，正常加载收藏数据');
    this.loadFavorites();
  },
});