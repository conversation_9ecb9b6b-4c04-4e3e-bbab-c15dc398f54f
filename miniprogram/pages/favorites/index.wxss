.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f4f4;
}

.scroll-view {
  flex: 1;
  height: 100%; /* Ensure scroll-view takes available height */
  box-sizing: border-box;
}

.favorites-list {
  padding: 10rpx 20rpx; /* Add some padding */
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh; /* Adjust height as needed */
  color: #999;
}

.empty-image {
  width: 200rpx; /* Adjust size as needed */
  height: 200rpx; /* Adjust size as needed */
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

.loading-more, .no-more {
  text-align: center;
  padding: 20rpx;
  color: #999;
  font-size: 24rpx;
}

/* 骨架屏样式 */
.skeleton-list {
  width: 100%;
  box-sizing: border-box;
}

.skeleton-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.skeleton-title {
  width: 95%;
  height: 36rpx;
  margin-bottom: 24rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-info {
  display: flex;
  gap: 16rpx;
}

.skeleton-tag {
  width: 120rpx;
  height: 32rpx;
  border-radius: 6rpx;
  background: linear-gradient(
    90deg,
    rgba(240, 240, 240, 1) 0%,
    rgba(250, 250, 250, 1) 50%,
    rgba(240, 240, 240, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.question-list {
  width: 100%;
  box-sizing: border-box;
}

.question-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin: 0 20rpx 20rpx 20rpx;
  box-sizing: border-box;
}

.question-title {
  font-size: 30rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.question-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.question-type, .question-company {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80vh;
  color: #999;
} 