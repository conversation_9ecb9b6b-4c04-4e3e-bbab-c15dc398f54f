<block wx:if="{{loading}}">
  <view class="loading-state">
    <text>加载中...</text>
  </view>
</block>
<scroll-view scroll-y class="scroll-view" wx:elif="{{!loading}}" bindscrolltolower="loadMoreFavorites">
  <view class="favorites-list" wx:if="{{favorites.length > 0}}">
    <block wx:for="{{favorites}}" wx:key="id">
      <!-- Use question-item component, mapping questionId to id -->
      <question-item question="{{ { id: item.questionId, title: item.title, companyName: item.companyName, pmTypeName: item.pmTypeName } }}" />
    </block>
    <view class="loading-more" wx:if="{{isLoading}}">加载中...</view>
    <view class="no-more" wx:if="{{!hasMore}}">没有更多了</view>
  </view>
  <view class="empty-state" wx:else>
    <text class="empty-text">暂无收藏</text>
  </view>
</scroll-view> 