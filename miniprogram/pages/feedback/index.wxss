/* feedback.wxss */
.container {
  background: linear-gradient(180deg, #f3f6fb 0%, #e8f2ff 100%);
  min-height: 100vh;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
  line-height: 1.4;
}

/* 反馈表单 */
.feedback-form {
  margin-bottom: 40rpx;
}

.form-item {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.required {
  color: #ff6b6b;
  font-size: 32rpx;
  margin-left: 8rpx;
}

.optional {
  color: #999;
  font-size: 24rpx;
  margin-left: 8rpx;
}

/* 文本域 */
.feedback-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e8f2ff;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafbfc;
  box-sizing: border-box;
  line-height: 1.5;
}

.feedback-textarea:focus {
  border-color: #409EFF;
  background: #ffffff;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 输入框 */
.contact-input {
  width: 100%;
  height: 88rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e8f2ff;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafbfc;
  box-sizing: border-box;
}

.contact-input:focus {
  border-color: #409EFF;
  background: #ffffff;
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  margin-bottom: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 24rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  margin-bottom: 16rpx;
}

.submit-btn.active {
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  box-shadow: 0 8rpx 24rpx rgba(64, 158, 255, 0.3);
}

.submit-btn.disabled {
  background: #e8e8e8;
  box-shadow: none;
}

.submit-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.4);
}

.submit-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}

.submit-btn.disabled .submit-text {
  color: #999;
}

.submit-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.4;
}

/* 建议卡片 */
.suggestion-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
}

.suggestion-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20rpx;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.suggestion-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 联系客服 */
.contact-section {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(64, 158, 255, 0.08);
  text-align: center;
}

.contact-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.contact-btn {
  width: 100%;
  height: 88rpx;
  background: #ffffff;
  border: 2rpx solid #409EFF;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.contact-btn:active {
  transform: scale(0.98);
  background: rgba(64, 158, 255, 0.05);
}

.contact-icon {
  width: 32rpx;
  height: 32rpx;
}

.contact-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #409EFF;
}

/* 成功弹窗 */
.success-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: overlayShow 0.3s ease-out;
}

.success-modal {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 60rpx 48rpx;
  margin: 0 48rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: modalShow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.success-btn {
  width: 200rpx;
  height: 72rpx;
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  border: none;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.success-btn:active {
  transform: scale(0.95);
}

/* 动画 */
@keyframes overlayShow {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes modalShow {
  0% {
    transform: scale(0.8) translateY(40rpx);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}
