interface IPageData {
  feedbackContent: string;
  contactInfo: string;
  canSubmit: boolean;
  submitting: boolean;
  showSuccess: boolean;
}

interface IPageInstance {
  data: IPageData;
  onLoad: () => void;
  onFeedbackInput: (e: any) => void;
  onContactInput: (e: any) => void;
  submitFeedback: () => void;
  goToContact: () => void;
  hideSuccess: () => void;
  stopPropagation: () => void;
  checkCanSubmit: () => void;
}

Page<IPageData, IPageInstance>({
  data: {
    feedbackContent: '',
    contactInfo: '',
    canSubmit: false,
    submitting: false,
    showSuccess: false
  },

  onLoad() {
    // 页面加载时的初始化
    wx.setNavigationBarTitle({
      title: '意见反馈'
    });
  },

  onFeedbackInput(e: any) {
    const value = e.detail.value;
    this.setData({
      feedbackContent: value
    }, () => {
      this.checkCanSubmit();
    });
  },

  onContactInput(e: any) {
    const value = e.detail.value;
    this.setData({ contactInfo: value });
  },

  checkCanSubmit() {
    const { feedbackContent } = this.data;
    const canSubmit = feedbackContent.trim().length >= 2; // 至少2个字符
    this.setData({ canSubmit });
  },

  async submitFeedback() {
    const { feedbackContent, contactInfo, submitting } = this.data;

    if (submitting) return;

    if (!feedbackContent.trim()) {
      wx.showToast({
        title: '请填写反馈意见',
        icon: 'none'
      });
      return;
    }

    if (feedbackContent.trim().length < 2) {
      wx.showToast({
        title: '反馈内容至少2个字符',
        icon: 'none'
      });
      return;
    }

    this.setData({ submitting: true });

    try {
      // 添加触觉反馈
      wx.vibrateShort({
        type: 'light'
      });

      // 调用维格表API
      const response: any = await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://api.vika.cn/fusion/v1/datasheets/dst1HPBBev7J8Gy3YS/records?viewId=viw8uX7MBfbtn&fieldKey=name',
          method: 'POST',
          header: {
            'Authorization': 'Bearer uskDKJz5VUZM43xECCqIXPI',
            'Content-Type': 'application/json'
          },
          data: {
            records: [
              {
                fields: {
                  '意见': feedbackContent.trim(),
                  '联系方式': contactInfo.trim() || '未填写'
                }
              }
            ],
            fieldKey: 'name'
          },
          success: resolve,
          fail: reject
        });
      });

      console.log('反馈提交响应:', response);

      // 维格表API成功判断：statusCode为200或201且data.success为true
      if ((response.statusCode === 200 || response.statusCode === 201) && response.data && response.data.success) {
        // 提交成功
        wx.vibrateShort({
          type: 'medium'
        });

        this.setData({
          submitting: false,
          showSuccess: true,
          feedbackContent: '',
          contactInfo: '',
          canSubmit: false
        });
      } else {
        // 提交失败，显示具体错误信息
        const errorMsg = response.data?.message || '提交失败';
        throw new Error(errorMsg);
      }

    } catch (error) {
      console.error('提交反馈失败:', error);
      this.setData({ submitting: false });

      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  goToContact() {
    wx.navigateTo({
      url: '/pages/contact/index'
    });
  },

  hideSuccess() {
    this.setData({ showSuccess: false });
  },

  stopPropagation() {
    // 阻止事件冒泡
  }
});
