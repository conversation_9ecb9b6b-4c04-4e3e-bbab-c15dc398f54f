<!--feedback.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <view class="title">意见反馈</view>
    <view class="subtitle">有任何意见和反馈，欢迎随时反馈</view>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-form">
    <!-- 反馈意见 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">反馈意见</text>
        <text class="required">*</text>
      </view>
      <textarea 
        class="feedback-textarea" 
        placeholder="请详细描述您遇到的问题或建议..."
        value="{{feedbackContent}}"
        bindinput="onFeedbackInput"
        maxlength="500"
        show-confirm-bar="{{false}}"
        auto-height
      ></textarea>
      <view class="char-count">{{feedbackContent.length}}/500</view>
    </view>

    <!-- 联系方式 -->
    <view class="form-item">
      <view class="form-label">
        <text class="label-text">联系方式</text>
        <text class="optional">(选填)</text>
      </view>
      <input 
        class="contact-input" 
        placeholder="请输入您的微信号，方便我们联系您"
        value="{{contactInfo}}"
        bindinput="onContactInput"
        maxlength="50"
      />
      <view class="input-tip">填写联系方式有助于我们更好地为您解决问题</view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <button 
      class="submit-btn {{canSubmit ? 'active' : 'disabled'}}" 
      bindtap="submitFeedback"
      disabled="{{!canSubmit || submitting}}"
    >
      <text class="submit-text">{{submitting ? '提交中...' : '提交反馈'}}</text>
    </button>
    
    <view class="submit-tip">
      我们会认真对待每一条反馈，感谢您的支持！
    </view>
  </view>

  <!-- 反馈建议 -->
  <view class="suggestion-card">
    <view class="suggestion-title">💡 反馈建议</view>
    <view class="suggestion-list">
      <view class="suggestion-item">• 详细描述问题的具体场景和操作步骤</view>
      <view class="suggestion-item">• 如有截图或错误信息，可通过微信发送给客服</view>
      <view class="suggestion-item">• 功能建议请说明具体的使用场景和期望效果</view>
      <view class="suggestion-item">• 留下联系方式可以获得更及时的回复</view>
    </view>
  </view>

  <!-- 联系客服 -->
  <view class="contact-section">
    <view class="contact-title">需要即时帮助？</view>
    <button class="contact-btn" bindtap="goToContact">
      <image src="/images/my/contact.svg" class="contact-icon" />
      <text class="contact-text">联系客服</text>
    </button>
  </view>
</view>

<!-- 提交成功弹窗 -->
<view class="success-overlay" wx:if="{{showSuccess}}" bindtap="hideSuccess">
  <view class="success-modal" catchtap="stopPropagation">
    <view class="success-icon">✅</view>
    <view class="success-title">反馈提交成功</view>
    <view class="success-desc">感谢您的反馈，我们会尽快处理并回复您</view>
    <button class="success-btn" bindtap="hideSuccess">确定</button>
  </view>
</view>
