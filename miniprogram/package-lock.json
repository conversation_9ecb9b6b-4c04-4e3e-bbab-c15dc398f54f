{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "dependencies": {"umtrack-wx": "^2.8.0"}, "devDependencies": {"miniprogram-api-typings": "^4.0.5", "typescript": "^5.3.3"}}, "node_modules/miniprogram-api-typings": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/miniprogram-api-typings/-/miniprogram-api-typings-4.0.7.tgz", "integrity": "sha512-sUxqpA1+JxnDWYjO6ihiLWNJl94WzjlhnDpozfBeC+w8Mpejjsc8JuxyC3Xisimj/gp5OYaJ7nd4fM6wepzhEQ==", "dev": true, "license": "MIT"}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmmirror.com/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/umtrack-wx": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/umtrack-wx/-/umtrack-wx-2.8.0.tgz", "integrity": "sha512-F5ul+Q7bDJ6MDrn9ysPAyB9nyP1vCxLGUBkSJ4uvknt8rjmX4tqy1IUnJuWKj9ZH2BtkjRFpldQXJSlLDOYfhQ==", "license": "ISC"}}}