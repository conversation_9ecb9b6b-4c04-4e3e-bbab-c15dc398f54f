/// <reference path="./types/index.d.ts" />
/// <reference path="../node_modules/miniprogram-api-typings/index.d.ts" />

interface IAppOption {
  globalData: {
    userInfo?: WechatMiniprogram.UserInfo,
  }
  userInfoReadyCallback?: WechatMiniprogram.GetUserInfoSuccessCallback,
}

interface ICustomPage<D extends WechatMiniprogram.Page.DataOption, T extends WechatMiniprogram.Page.InstanceProperties> {
  data?: D;
  onLoad?(): void;
  onShow?(): void;
  onReady?(): void;
  onHide?(): void;
  onUnload?(): void;
  onPullDownRefresh?(): void;
  onReachBottom?(): void;
  onShareAppMessage?(): WechatMiniprogram.Page.ICustomShareContent;
  onPageScroll?(): void;
  onTabItemTap?(): void;
  onCollectionTap?(): void;
  onStatsTap?(): void;
  onSettingsTap?(): void;
  onHelpTap?(): void;
  onShareTap?(): void;
  onLogout?(): void;
}