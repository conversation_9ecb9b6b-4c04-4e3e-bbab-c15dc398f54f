{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./miniprogram/package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": false, "urlCheck": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.3.0", "packOptions": {"ignore": [], "include": []}, "appid": "wx45b4e49879846395"}